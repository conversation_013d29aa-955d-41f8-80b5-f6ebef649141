0000000000000000000000000000000000000000 f7e2074cc5c5379d2a1a9c5f1fba868ac4697fc7 Sov3rain <<EMAIL>> 1758097431 +0200	clone: from https://github.com/Sov3rain/unity-standalone-file-browser.git
f7e2074cc5c5379d2a1a9c5f1fba868ac4697fc7 3dd75ba380ccc2897741b63be76eaa447fe3c0dd Sov3rain <<EMAIL>> 1758097548 +0200	commit: chore: update to unity 2021.3.45f1
3dd75ba380ccc2897741b63be76eaa447fe3c0dd e6af7726ebbeb628d32f57e1e747a77b150959dc Sov3rain <<EMAIL>> 1758097746 +0200	commit: chore: update rider package
e6af7726ebbeb628d32f57e1e747a77b150959dc 677efa742ef4e324cece903f1863cbee347126d0 Sov3rain <<EMAIL>> 1758099089 +0200	commit: chore: add TextMeshPro package, move Editor folder out of runtime folder, format and cleanup code and comments
677efa742ef4e324cece903f1863cbee347126d0 db781ce6524fcea2f9d3ced3f337bdd4910ac0cd Sov3rain <<EMAIL>> 1758110391 +0200	commit: chore: refactor file and folder panel methods to return `FileSystemInfo` objects, update related demos and improve code readability
db781ce6524fcea2f9d3ced3f337bdd4910ac0cd 32d8e625a9b7d1b4dae5c6259f9becd3e946b3d0 Sov3rain <<EMAIL>> 1758112330 +0200	commit: chore: enhance `StandaloneFileBrowser` API with improved parameter validation, updated XML documentation, and consistent null-handling
32d8e625a9b7d1b4dae5c6259f9becd3e946b3d0 4f67dccbddded919031832624e2a81e55abbf821 Sov3rain <<EMAIL>> 1758112575 +0200	commit: chore: remove redundant null checks for callbacks in `StandaloneFileBrowser` methods, update XML documentation to reflect optional nature
4f67dccbddded919031832624e2a81e55abbf821 5314190432f740a97dfddb74d077ff3c2b9ef20c Sov3rain <<EMAIL>> 1758114945 +0200	commit: chore: add optional callback null-checks to `StandaloneFileBrowser` methods, update demos with examples for no-callback usage
