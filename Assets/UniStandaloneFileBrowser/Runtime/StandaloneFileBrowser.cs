using System;
using System.IO;

namespace USFB
{
    /// <summary>
    /// Cross-platform file browser API that provides native file dialogs
    /// and returns strongly typed FileInfo and DirectoryInfo objects
    /// </summary>
    public static class StandaloneFileBrowser
    {
        private static readonly IStandaloneFileBrowser _platformWrapper;

        static StandaloneFileBrowser()
        {
#if UNITY_EDITOR
            _platformWrapper = new StandaloneFileBrowserEditor();
#elif UNITY_STANDALONE_OSX
            _platformWrapper = new StandaloneFileBrowserMac();
#elif UNITY_STANDALONE_WIN
            _platformWrapper = new StandaloneFileBrowserWindows();
#elif UNITY_STANDALONE_LINUX
            _platformWrapper = new StandaloneFileBrowserLinux();
#else
            throw new NotSupportedException("Standalone file dialogs are not supported on this platform.");
#endif
        }

        /// <summary>
        /// Native open file dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="extension">Allowed extension; null or empty allows all files</param>
        /// <param name="multiselect">Allow multiple file selection</param>
        /// <returns>An array of selected files; empty when canceled</returns>
        public static FileInfo[] OpenFilePanel(string title, string directory, string extension, bool multiselect)
        {
            var extensions = string.IsNullOrEmpty(extension) ? null : new[] { new ExtensionFilter("", extension) };
            return OpenFilePanel(title, directory, extensions, multiselect);
        }

        /// <summary>
        /// Native open file dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="extensions">List of extension filters; null allows all files. Filter Example: new ExtensionFilter("Image Files", "jpg", "png")</param>
        /// <param name="multiselect">Allow multiple file selection</param>
        /// <returns>An array of selected files; empty when canceled</returns>
        public static FileInfo[] OpenFilePanel(
            string title,
            string directory,
            ExtensionFilter[] extensions,
            bool multiselect)
        {
            var paths = _platformWrapper.OpenFilePanel(title, directory, extensions, multiselect) ?? Array.Empty<string>();
            var fileInfos = new FileInfo[paths.Length];

            for (int i = 0; i < paths.Length; i++)
            {
                fileInfos[i] = new FileInfo(paths[i]);
            }

            return fileInfos;
        }

        /// <summary>
        /// Native open file dialog async
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="extension">Allowed extension; null or empty allows all files</param>
        /// <param name="multiselect">Allow multiple file selection</param>
        /// <param name="callback">Optional callback invoked with the selected files (empty array on cancel); can be null</param>
        public static void OpenFilePanelAsync(
            string title,
            string directory,
            string extension,
            bool multiselect,
            Action<FileInfo[]> callback)
        {
            var extensions = string.IsNullOrEmpty(extension) ? null : new[] { new ExtensionFilter("", extension) };
            OpenFilePanelAsync(title, directory, extensions, multiselect, callback);
        }

        /// <summary>
        /// Native open file dialog async
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="extensions">List of extension filters; null allows all files. Filter Example: new ExtensionFilter("Image Files", "jpg", "png")</param>
        /// <param name="multiselect">Allow multiple file selection</param>
        /// <param name="callback">Optional callback invoked with the selected files (empty array on cancel); can be null</param>
        public static void OpenFilePanelAsync(
            string title,
            string directory,
            ExtensionFilter[] extensions,
            bool multiselect,
            Action<FileInfo[]> callback)
        {
            void CallbackWrapper(string[] paths)
            {
                var pathsArray = paths ?? Array.Empty<string>();
                var fileInfos = new FileInfo[pathsArray.Length];
                for (int i = 0; i < pathsArray.Length; i++)
                {
                    fileInfos[i] = new FileInfo(pathsArray[i]);
                }

                callback?.Invoke(fileInfos);
            }

            _platformWrapper.OpenFilePanelAsync(title, directory, extensions, multiselect, CallbackWrapper);
        }

        /// <summary>
        /// Native open folder dialog
        /// NOTE: Multiple folder selection is not supported on Windows
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="multiselect">Allow multiple folder selection</param>
        /// <returns>An array of selected directories; empty when canceled</returns>
        public static DirectoryInfo[] OpenFolderPanel(string title, string directory, bool multiselect)
        {
            var paths = _platformWrapper.OpenFolderPanel(title, directory, multiselect) ?? Array.Empty<string>();
            var directoryInfos = new DirectoryInfo[paths.Length];

            for (int i = 0; i < paths.Length; i++)
            {
                directoryInfos[i] = new DirectoryInfo(paths[i]);
            }

            return directoryInfos;
        }

        /// <summary>
        /// Native open folder dialog async
        /// NOTE: Multiple folder selection is not supported on Windows
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="multiselect">Allow multiple folder selection</param>
        /// <param name="callback">Optional callback invoked with the selected directories (empty array on cancel); can be null</param>
        public static void OpenFolderPanelAsync(
            string title,
            string directory,
            bool multiselect,
            Action<DirectoryInfo[]> callback)
        {
            void CallbackWrapper(string[] paths)
            {
                var pathsArray = paths ?? Array.Empty<string>();
                var directoryInfos = new DirectoryInfo[pathsArray.Length];
                for (int i = 0; i < pathsArray.Length; i++)
                {
                    directoryInfos[i] = new DirectoryInfo(pathsArray[i]);
                }

                callback?.Invoke(directoryInfos);
            }

            _platformWrapper.OpenFolderPanelAsync(title, directory, multiselect, CallbackWrapper);
        }

        /// <summary>
        /// Native save file dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="defaultName">Default file name</param>
        /// <param name="extension">File extension; null or empty allows all files</param>
        /// <returns>The chosen FileInfo; null when canceled</returns>
        public static FileInfo SaveFilePanel(string title, string directory, string defaultName, string extension)
        {
            var extensions = string.IsNullOrEmpty(extension) ? null : new[] { new ExtensionFilter("", extension) };
            return SaveFilePanel(title, directory, defaultName, extensions);
        }

        /// <summary>
        /// Native save file dialog
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="defaultName">Default file name</param>
        /// <param name="extensions">List of extension filters; null allows all files. Filter Example: new ExtensionFilter("Image Files", "jpg", "png")</param>
        /// <returns>The chosen FileInfo; null when canceled</returns>
        public static FileInfo SaveFilePanel(
            string title,
            string directory,
            string defaultName,
            ExtensionFilter[] extensions)
        {
            var path = _platformWrapper.SaveFilePanel(title, directory, defaultName, extensions);
            return string.IsNullOrEmpty(path) ? null : new FileInfo(path);
        }

        /// <summary>
        /// Native save file dialog async
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="defaultName">Default file name</param>
        /// <param name="extension">File extension; null or empty allows all files</param>
        /// <param name="callback">Optional callback invoked with the chosen FileInfo (null on cancel); can be null</param>
        public static void SaveFilePanelAsync(
            string title,
            string directory,
            string defaultName,
            string extension,
            Action<FileInfo> callback)
        {
            var extensions = string.IsNullOrEmpty(extension) ? null : new[] { new ExtensionFilter("", extension) };
            SaveFilePanelAsync(title, directory, defaultName, extensions, callback);
        }

        /// <summary>
        /// Native save file dialog async
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="directory">Root directory</param>
        /// <param name="defaultName">Default file name</param>
        /// <param name="extensions">List of extension filters; null allows all files. Filter Example: new ExtensionFilter("Image Files", "jpg", "png")</param>
        /// <param name="callback">Optional callback invoked with the chosen FileInfo (null on cancel); can be null</param>
        public static void SaveFilePanelAsync(
            string title,
            string directory,
            string defaultName,
            ExtensionFilter[] extensions,
            Action<FileInfo> callback)
        {
            void CallbackWrapper(string path)
            {
                callback?.Invoke(string.IsNullOrEmpty(path) ? null : new FileInfo(path));
            }

            _platformWrapper.SaveFilePanelAsync(title, directory, defaultName, extensions, CallbackWrapper);
        }
    }
}