using System;
using System.Collections.Generic;
using NUnit.Framework;
using USFB;
using USFB.Tests;
using USFB.Tests.Mocks;

namespace USFB.Tests
{
    /// <summary>
    /// Tests for platform-specific behavior and implementations.
    /// Verifies that different platforms handle file dialogs correctly using mock implementations.
    /// </summary>
    [TestFixture]
    public class PlatformSpecificTests
    {
        private List<MockPlatformWrapper> _platformWrappers;

        #region Setup and Teardown

        /// <summary>
        /// Sets up test environment with mock platform wrappers for each supported platform.
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            _platformWrappers = new List<MockPlatformWrapper>
            {
                new MockPlatformWrapper(MockPlatformWrapper.PlatformType.Windows),
                new MockPlatformWrapper(MockPlatformWrapper.PlatformType.Mac),
                new MockPlatformWrapper(MockPlatformWrapper.PlatformType.Linux),
                new MockPlatformWrapper(MockPlatformWrapper.PlatformType.Editor)
            };
        }

        /// <summary>
        /// Cleans up test environment after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            _platformWrappers?.Clear();
        }

        #endregion

        #region Filter String Generation Tests

        /// <summary>
        /// Tests that each platform generates filter strings in the correct format.
        /// </summary>
        [Test]
        public void PlatformFilterGeneration_ForEachPlatform_GeneratesCorrectFormat()
        {
            // Arrange
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                Assert.DoesNotThrow(() =>
                {
                    var filterString = wrapper.GetPlatformSpecificFilterString(extensions);
                    Assert.IsNotNull(filterString, $"Filter string should not be null for {wrapper.Platform}");
                    
                    // Verify platform-specific characteristics
                    switch (wrapper.Platform)
                    {
                        case MockPlatformWrapper.PlatformType.Windows:
                            // Windows typically uses pipe separators
                            if (!string.IsNullOrEmpty(filterString))
                                Assert.IsTrue(filterString.Contains("|") || extensions.Length <= 1, 
                                    "Windows filter should contain pipe separators for multiple filters");
                            break;
                            
                        case MockPlatformWrapper.PlatformType.Mac:
                        case MockPlatformWrapper.PlatformType.Linux:
                            // Unix platforms may use different separators
                            Assert.IsInstanceOf<string>(filterString, 
                                $"Unix filter should be string for {wrapper.Platform}");
                            break;
                            
                        case MockPlatformWrapper.PlatformType.Editor:
                            // Editor format is specific to Unity
                            Assert.IsInstanceOf<string>(filterString, 
                                "Editor filter should be string");
                            break;
                    }
                }, $"Filter generation should work for {wrapper.Platform}");
            }
        }

        /// <summary>
        /// Tests filter generation with null extensions across all platforms.
        /// </summary>
        [Test]
        public void PlatformFilterGeneration_WithNullExtensions_HandlesGracefully()
        {
            // Arrange
            ExtensionFilter[] nullExtensions = null;

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                Assert.DoesNotThrow(() =>
                {
                    var filterString = wrapper.GetPlatformSpecificFilterString(nullExtensions);
                    Assert.IsNotNull(filterString, $"Filter string should not be null for {wrapper.Platform} with null extensions");
                }, $"Null extension handling should work for {wrapper.Platform}");
            }
        }

        /// <summary>
        /// Tests filter generation with edge case extensions across all platforms.
        /// </summary>
        [Test]
        public void PlatformFilterGeneration_WithEdgeCaseExtensions_HandlesCorrectly()
        {
            // Arrange
            var edgeCaseExtensions = TestUtilities.CreateEdgeCaseExtensionFilters();

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                Assert.DoesNotThrow(() =>
                {
                    var filterString = wrapper.GetPlatformSpecificFilterString(edgeCaseExtensions);
                    Assert.IsNotNull(filterString, $"Filter string should not be null for {wrapper.Platform} with edge cases");
                }, $"Edge case extension handling should work for {wrapper.Platform}");
            }
        }

        #endregion

        #region Path Formatting Tests

        /// <summary>
        /// Tests that each platform formats paths correctly.
        /// </summary>
        [Test]
        public void PlatformPathFormatting_ForEachPlatform_UsesCorrectSeparators()
        {
            // Arrange
            const string mixedPath = @"C:\Test/Path\With/Mixed\Separators";

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                var formattedPath = wrapper.FormatPathForPlatform(mixedPath);
                
                switch (wrapper.Platform)
                {
                    case MockPlatformWrapper.PlatformType.Windows:
                        Assert.IsFalse(formattedPath.Contains('/'), 
                            "Windows paths should not contain forward slashes");
                        Assert.IsTrue(formattedPath.Contains('\\'), 
                            "Windows paths should contain backslashes");
                        break;
                        
                    case MockPlatformWrapper.PlatformType.Mac:
                    case MockPlatformWrapper.PlatformType.Linux:
                    case MockPlatformWrapper.PlatformType.Editor:
                        Assert.IsFalse(formattedPath.Contains('\\'), 
                            $"{wrapper.Platform} paths should not contain backslashes");
                        Assert.IsTrue(formattedPath.Contains('/'), 
                            $"{wrapper.Platform} paths should contain forward slashes");
                        break;
                }
            }
        }

        /// <summary>
        /// Tests path formatting with null and empty paths.
        /// </summary>
        [Test]
        public void PlatformPathFormatting_WithNullAndEmptyPaths_HandlesGracefully()
        {
            // Arrange
            string[] testPaths = { null, "", "   " };

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                foreach (var testPath in testPaths)
                {
                    Assert.DoesNotThrow(() =>
                    {
                        var formattedPath = wrapper.FormatPathForPlatform(testPath);
                        // Should return the same value for null/empty paths
                        Assert.AreEqual(testPath, formattedPath, 
                            $"Null/empty path handling should be consistent for {wrapper.Platform}");
                    }, $"Path formatting should handle null/empty paths for {wrapper.Platform}");
                }
            }
        }

        #endregion

        #region Dialog Behavior Tests

        /// <summary>
        /// Tests that each platform reports correct multiselect support.
        /// </summary>
        [Test]
        public void PlatformDialogBehavior_MultiselectSupport_ReportsCorrectly()
        {
            // Arrange
            var dialogTypes = new[] { DialogType.OpenFile, DialogType.OpenFolder, DialogType.SaveFile };

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                foreach (var dialogType in dialogTypes)
                {
                    var behavior = wrapper.GetDialogBehavior(dialogType, true);
                    
                    Assert.AreEqual(wrapper.Platform, behavior.Platform, 
                        "Behavior should report correct platform");
                    Assert.AreEqual(dialogType, behavior.DialogType, 
                        "Behavior should report correct dialog type");
                    
                    // Verify platform-specific multiselect support
                    switch (wrapper.Platform)
                    {
                        case MockPlatformWrapper.PlatformType.Windows:
                            Assert.IsTrue(behavior.SupportsMultiselect, 
                                "Windows should support multiselect for all dialog types");
                            break;
                            
                        case MockPlatformWrapper.PlatformType.Mac:
                            if (dialogType == DialogType.SaveFile)
                                Assert.IsFalse(behavior.SupportsMultiselect, 
                                    "Mac should not support multiselect for save dialogs");
                            else
                                Assert.IsTrue(behavior.SupportsMultiselect, 
                                    "Mac should support multiselect for open dialogs");
                            break;
                            
                        case MockPlatformWrapper.PlatformType.Linux:
                            if (dialogType == DialogType.OpenFile)
                                Assert.IsTrue(behavior.SupportsMultiselect, 
                                    "Linux should support multiselect for file open");
                            else
                                Assert.IsFalse(behavior.SupportsMultiselect, 
                                    "Linux should have limited multiselect support");
                            break;
                            
                        case MockPlatformWrapper.PlatformType.Editor:
                            if (dialogType == DialogType.OpenFile)
                                Assert.IsTrue(behavior.SupportsMultiselect, 
                                    "Editor should support multiselect for file open");
                            else
                                Assert.IsFalse(behavior.SupportsMultiselect, 
                                    "Editor should have limited multiselect support");
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// Tests that each platform reports correct async support.
        /// </summary>
        [Test]
        public void PlatformDialogBehavior_AsyncSupport_ReportsCorrectly()
        {
            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                var behavior = wrapper.GetDialogBehavior(DialogType.OpenFile, false);
                
                switch (wrapper.Platform)
                {
                    case MockPlatformWrapper.PlatformType.Windows:
                    case MockPlatformWrapper.PlatformType.Mac:
                    case MockPlatformWrapper.PlatformType.Linux:
                        Assert.IsTrue(behavior.SupportsAsync, 
                            $"{wrapper.Platform} should support async operations");
                        break;
                        
                    case MockPlatformWrapper.PlatformType.Editor:
                        Assert.IsFalse(behavior.SupportsAsync, 
                            "Editor should not support true async (immediate callback)");
                        break;
                }
            }
        }

        /// <summary>
        /// Tests that each platform reports correct path separator.
        /// </summary>
        [Test]
        public void PlatformDialogBehavior_PathSeparator_ReportsCorrectly()
        {
            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                var behavior = wrapper.GetDialogBehavior(DialogType.OpenFile, false);
                
                switch (wrapper.Platform)
                {
                    case MockPlatformWrapper.PlatformType.Windows:
                        Assert.AreEqual('\\', behavior.ExpectedPathSeparator, 
                            "Windows should use backslash separator");
                        break;
                        
                    case MockPlatformWrapper.PlatformType.Mac:
                    case MockPlatformWrapper.PlatformType.Linux:
                    case MockPlatformWrapper.PlatformType.Editor:
                        Assert.AreEqual('/', behavior.ExpectedPathSeparator, 
                            $"{wrapper.Platform} should use forward slash separator");
                        break;
                }
            }
        }

        /// <summary>
        /// Tests that all platforms report Unicode support.
        /// </summary>
        [Test]
        public void PlatformDialogBehavior_UnicodeSupport_AllPlatformsSupport()
        {
            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                var behavior = wrapper.GetDialogBehavior(DialogType.OpenFile, false);
                Assert.IsTrue(behavior.SupportsUnicode, 
                    $"{wrapper.Platform} should support Unicode characters");
            }
        }

        #endregion

        #region Cross-Platform Consistency Tests

        /// <summary>
        /// Tests that all platforms handle the same input consistently.
        /// </summary>
        [Test]
        public void CrossPlatformConsistency_WithSameInput_ProducesConsistentResults()
        {
            // Arrange
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const string testPath = @"C:\Test\Path";

            // Act & Assert
            foreach (var wrapper in _platformWrappers)
            {
                Assert.DoesNotThrow(() =>
                {
                    // All platforms should be able to process the same inputs
                    var filterString = wrapper.GetPlatformSpecificFilterString(extensions);
                    var formattedPath = wrapper.FormatPathForPlatform(testPath);
                    var behavior = wrapper.GetDialogBehavior(DialogType.OpenFile, true);
                    
                    Assert.IsNotNull(filterString, $"Filter string should not be null for {wrapper.Platform}");
                    Assert.IsNotNull(formattedPath, $"Formatted path should not be null for {wrapper.Platform}");
                    Assert.IsNotNull(behavior, $"Behavior should not be null for {wrapper.Platform}");
                }, $"Platform {wrapper.Platform} should handle standard inputs consistently");
            }
        }

        /// <summary>
        /// Tests that platform differences are documented and expected.
        /// </summary>
        [Test]
        public void CrossPlatformDifferences_AreDocumentedAndExpected()
        {
            // This test documents known platform differences
            var windowsWrapper = _platformWrappers.Find(w => w.Platform == MockPlatformWrapper.PlatformType.Windows);
            var macWrapper = _platformWrappers.Find(w => w.Platform == MockPlatformWrapper.PlatformType.Mac);
            var linuxWrapper = _platformWrappers.Find(w => w.Platform == MockPlatformWrapper.PlatformType.Linux);
            var editorWrapper = _platformWrappers.Find(w => w.Platform == MockPlatformWrapper.PlatformType.Editor);

            // Path separator differences
            Assert.AreEqual('\\', windowsWrapper.GetDialogBehavior(DialogType.OpenFile, false).ExpectedPathSeparator);
            Assert.AreEqual('/', macWrapper.GetDialogBehavior(DialogType.OpenFile, false).ExpectedPathSeparator);
            Assert.AreEqual('/', linuxWrapper.GetDialogBehavior(DialogType.OpenFile, false).ExpectedPathSeparator);
            Assert.AreEqual('/', editorWrapper.GetDialogBehavior(DialogType.OpenFile, false).ExpectedPathSeparator);

            // Async support differences
            Assert.IsTrue(windowsWrapper.GetDialogBehavior(DialogType.OpenFile, false).SupportsAsync);
            Assert.IsTrue(macWrapper.GetDialogBehavior(DialogType.OpenFile, false).SupportsAsync);
            Assert.IsTrue(linuxWrapper.GetDialogBehavior(DialogType.OpenFile, false).SupportsAsync);
            Assert.IsFalse(editorWrapper.GetDialogBehavior(DialogType.OpenFile, false).SupportsAsync);

            // These differences are expected and documented
        }

        #endregion
    }
}
