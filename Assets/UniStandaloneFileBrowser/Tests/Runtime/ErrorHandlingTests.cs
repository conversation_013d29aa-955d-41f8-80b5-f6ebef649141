using System;
using System.Collections;
using System.IO;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using USFB;
using USFB.Tests;
using USFB.Tests.Mocks;

namespace USFB.Tests
{
    /// <summary>
    /// Comprehensive tests for error handling, edge cases, and null parameter scenarios.
    /// Verifies that the StandaloneFileBrowser API handles all error conditions gracefully.
    /// </summary>
    [TestFixture]
    public class ErrorHandlingTests
    {
        #region Null Parameter Tests

        /// <summary>
        /// Tests OpenFilePanel with all null parameters.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithAllNullParameters_HandlesGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(null, null, null, false);
                Assert.IsNotNull(result, "Result should not be null even with all null parameters");
                Assert.IsInstanceOf<FileInfo[]>(result, "Result should be FileInfo array");
            }, "OpenFilePanel should handle all null parameters gracefully");
        }

        /// <summary>
        /// Tests OpenFolderPanel with null parameters.
        /// </summary>
        [Test]
        public void OpenFolderPanel_WithNullParameters_HandlesGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFolderPanel(null, null, false);
                Assert.IsNotNull(result, "Result should not be null with null parameters");
                Assert.IsInstanceOf<DirectoryInfo[]>(result, "Result should be DirectoryInfo array");
            }, "OpenFolderPanel should handle null parameters gracefully");
        }

        /// <summary>
        /// Tests SaveFilePanel with null parameters.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithNullParameters_HandlesGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.SaveFilePanel(null, null, null, null);
                Assert.IsNotNull(result, "Result should not be null with null parameters");
                Assert.IsInstanceOf<string>(result, "Result should be string");
            }, "SaveFilePanel should handle null parameters gracefully");
        }

        /// <summary>
        /// Tests async methods with null callbacks.
        /// </summary>
        [Test]
        public void AsyncMethods_WithNullCallbacks_HandleGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFilePanelAsync("Test", "", null, false, null);
                StandaloneFileBrowser.OpenFolderPanelAsync("Test", "", false, null);
                StandaloneFileBrowser.SaveFilePanelAsync("Test", "", "", null, null);
            }, "Async methods should handle null callbacks gracefully");
        }

        #endregion

        #region Empty String Parameter Tests

        /// <summary>
        /// Tests all methods with empty string parameters.
        /// </summary>
        [Test]
        public void AllMethods_WithEmptyStringParameters_HandleGracefully()
        {
            // Arrange
            const string emptyString = "";
            var emptyExtensions = new ExtensionFilter[0];

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var fileResult = StandaloneFileBrowser.OpenFilePanel(emptyString, emptyString, emptyExtensions, false);
                var folderResult = StandaloneFileBrowser.OpenFolderPanel(emptyString, emptyString, false);
                var saveResult = StandaloneFileBrowser.SaveFilePanel(emptyString, emptyString, emptyString, emptyExtensions);
                
                Assert.IsNotNull(fileResult, "File result should not be null with empty strings");
                Assert.IsNotNull(folderResult, "Folder result should not be null with empty strings");
                Assert.IsNotNull(saveResult, "Save result should not be null with empty strings");
            }, "All methods should handle empty string parameters gracefully");
        }

        /// <summary>
        /// Tests methods with whitespace-only parameters.
        /// </summary>
        [Test]
        public void AllMethods_WithWhitespaceParameters_HandleGracefully()
        {
            // Arrange
            const string whitespaceString = "   \t\n\r   ";

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var fileResult = StandaloneFileBrowser.OpenFilePanel(whitespaceString, whitespaceString, null, false);
                var folderResult = StandaloneFileBrowser.OpenFolderPanel(whitespaceString, whitespaceString, false);
                var saveResult = StandaloneFileBrowser.SaveFilePanel(whitespaceString, whitespaceString, whitespaceString, null);
                
                Assert.IsNotNull(fileResult, "File result should not be null with whitespace strings");
                Assert.IsNotNull(folderResult, "Folder result should not be null with whitespace strings");
                Assert.IsNotNull(saveResult, "Save result should not be null with whitespace strings");
            }, "All methods should handle whitespace-only parameters gracefully");
        }

        #endregion

        #region Invalid Path Tests

        /// <summary>
        /// Tests methods with invalid path characters.
        /// </summary>
        [Test]
        public void AllMethods_WithInvalidPathCharacters_HandleGracefully()
        {
            // Arrange
            var invalidPaths = new[]
            {
                @"C:\Invalid<>Path",
                @"C:\Path|With*Invalid:Chars",
                @"C:\Path\With""Quotes",
                @"C:\Path\With?Question",
                "Invalid/Path/With\0NullChar"
            };

            // Act & Assert
            foreach (var invalidPath in invalidPaths)
            {
                Assert.DoesNotThrow(() =>
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", invalidPath, null, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel("Test", invalidPath, false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel("Test", invalidPath, "test.txt", null);
                    
                    Assert.IsNotNull(fileResult, $"File result should not be null with invalid path: {invalidPath}");
                    Assert.IsNotNull(folderResult, $"Folder result should not be null with invalid path: {invalidPath}");
                    Assert.IsNotNull(saveResult, $"Save result should not be null with invalid path: {invalidPath}");
                }, $"Methods should handle invalid path characters gracefully: {invalidPath}");
            }
        }

        /// <summary>
        /// Tests methods with extremely long paths.
        /// </summary>
        [Test]
        public void AllMethods_WithExtremelyLongPaths_HandleGracefully()
        {
            // Arrange
            var longPath = @"C:\" + new string('A', 1000) + @"\" + new string('B', 1000);

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", longPath, null, false);
                var folderResult = StandaloneFileBrowser.OpenFolderPanel("Test", longPath, false);
                var saveResult = StandaloneFileBrowser.SaveFilePanel("Test", longPath, "test.txt", null);
                
                Assert.IsNotNull(fileResult, "File result should not be null with long path");
                Assert.IsNotNull(folderResult, "Folder result should not be null with long path");
                Assert.IsNotNull(saveResult, "Save result should not be null with long path");
            }, "Methods should handle extremely long paths gracefully");
        }

        /// <summary>
        /// Tests methods with non-existent paths.
        /// </summary>
        [Test]
        public void AllMethods_WithNonExistentPaths_HandleGracefully()
        {
            // Arrange
            var nonExistentPaths = new[]
            {
                @"C:\NonExistent\Directory\Path",
                @"Z:\CompletlyFakeDrive\Path",
                @"\\NonExistentServer\Share\Path"
            };

            // Act & Assert
            foreach (var path in nonExistentPaths)
            {
                Assert.DoesNotThrow(() =>
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", path, null, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel("Test", path, false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel("Test", path, "test.txt", null);
                    
                    Assert.IsNotNull(fileResult, $"File result should not be null with non-existent path: {path}");
                    Assert.IsNotNull(folderResult, $"Folder result should not be null with non-existent path: {path}");
                    Assert.IsNotNull(saveResult, $"Save result should not be null with non-existent path: {path}");
                }, $"Methods should handle non-existent paths gracefully: {path}");
            }
        }

        #endregion

        #region Unicode and Special Character Tests

        /// <summary>
        /// Tests methods with Unicode characters in all parameters.
        /// </summary>
        [Test]
        public void AllMethods_WithUnicodeCharacters_HandleCorrectly()
        {
            // Arrange
            const string unicodeTitle = "打开文件 📁 Öppna fil Открыть файл";
            const string unicodePath = @"C:\测试文件夹\Тестовая папка\Testordner";
            const string unicodeFileName = "测试文件_тест_test_🎉.txt";
            var unicodeExtensions = new[]
            {
                new ExtensionFilter("文本文件 (Text Files)", "txt", "текст"),
                new ExtensionFilter("图像文件 (Image Files)", "png", "jpg", "изображение")
            };

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var fileResult = StandaloneFileBrowser.OpenFilePanel(unicodeTitle, unicodePath, unicodeExtensions, false);
                var folderResult = StandaloneFileBrowser.OpenFolderPanel(unicodeTitle, unicodePath, false);
                var saveResult = StandaloneFileBrowser.SaveFilePanel(unicodeTitle, unicodePath, unicodeFileName, unicodeExtensions);
                
                Assert.IsNotNull(fileResult, "File result should not be null with Unicode characters");
                Assert.IsNotNull(folderResult, "Folder result should not be null with Unicode characters");
                Assert.IsNotNull(saveResult, "Save result should not be null with Unicode characters");
            }, "Methods should handle Unicode characters correctly");
        }

        /// <summary>
        /// Tests methods with special control characters.
        /// </summary>
        [Test]
        public void AllMethods_WithControlCharacters_HandleGracefully()
        {
            // Arrange
            var controlCharStrings = new[]
            {
                "Title\0WithNull",
                "Title\tWithTab",
                "Title\nWithNewline",
                "Title\rWithCarriageReturn",
                "Title\x1FWithUnitSeparator"
            };

            // Act & Assert
            foreach (var controlString in controlCharStrings)
            {
                Assert.DoesNotThrow(() =>
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel(controlString, "", null, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel(controlString, "", false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel(controlString, "", "test.txt", null);
                    
                    Assert.IsNotNull(fileResult, $"File result should not be null with control characters: {controlString}");
                    Assert.IsNotNull(folderResult, $"Folder result should not be null with control characters: {controlString}");
                    Assert.IsNotNull(saveResult, $"Save result should not be null with control characters: {controlString}");
                }, $"Methods should handle control characters gracefully: {controlString}");
            }
        }

        #endregion

        #region Extension Filter Error Tests

        /// <summary>
        /// Tests methods with malformed extension filters.
        /// </summary>
        [Test]
        public void AllMethods_WithMalformedExtensionFilters_HandleGracefully()
        {
            // Arrange
            var malformedFilters = new[]
            {
                new ExtensionFilter(null, "txt"), // Null name
                new ExtensionFilter("", "txt"), // Empty name
                new ExtensionFilter("Valid Name", (string[])null), // Null extensions
                new ExtensionFilter("Valid Name", new string[0]), // Empty extensions
                new ExtensionFilter("Valid Name", "", null, "txt"), // Mixed null and empty extensions
                new ExtensionFilter("Valid Name", "ext.with.dots", "ext with spaces", "EXT_WITH_UNDERSCORES")
            };

            // Act & Assert
            foreach (var filter in malformedFilters)
            {
                var filterArray = new[] { filter };
                
                Assert.DoesNotThrow(() =>
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", "", filterArray, false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel("Test", "", "test.txt", filterArray);
                    
                    Assert.IsNotNull(fileResult, $"File result should not be null with malformed filter: {filter.Name}");
                    Assert.IsNotNull(saveResult, $"Save result should not be null with malformed filter: {filter.Name}");
                }, $"Methods should handle malformed extension filters gracefully: {filter.Name}");
            }
        }

        /// <summary>
        /// Tests methods with extremely large extension filter arrays.
        /// </summary>
        [Test]
        public void AllMethods_WithLargeExtensionFilterArrays_HandleGracefully()
        {
            // Arrange
            var largeFilterArray = new ExtensionFilter[10000];
            for (int i = 0; i < largeFilterArray.Length; i++)
            {
                largeFilterArray[i] = new ExtensionFilter($"Filter {i}", $"ext{i}");
            }

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", "", largeFilterArray, false);
                var saveResult = StandaloneFileBrowser.SaveFilePanel("Test", "", "test.txt", largeFilterArray);
                
                Assert.IsNotNull(fileResult, "File result should not be null with large filter array");
                Assert.IsNotNull(saveResult, "Save result should not be null with large filter array");
            }, "Methods should handle large extension filter arrays gracefully");
        }

        #endregion

        #region Async Error Handling Tests

        /// <summary>
        /// Tests async methods with callbacks that throw exceptions.
        /// </summary>
        [Test]
        public void AsyncMethods_WithExceptionThrowingCallbacks_HandleGracefully()
        {
            // Arrange
            Action<FileInfo[]> fileCallback = (files) => throw new InvalidOperationException("Test exception");
            Action<DirectoryInfo[]> folderCallback = (folders) => throw new InvalidOperationException("Test exception");
            Action<string> saveCallback = (path) => throw new InvalidOperationException("Test exception");

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFilePanelAsync("Test", "", null, false, fileCallback);
                StandaloneFileBrowser.OpenFolderPanelAsync("Test", "", false, folderCallback);
                StandaloneFileBrowser.SaveFilePanelAsync("Test", "", "test.txt", null, saveCallback);
            }, "Async methods should handle exception-throwing callbacks gracefully");
        }

        /// <summary>
        /// Tests async methods with callbacks that access disposed objects.
        /// </summary>
        [UnityTest]
        public IEnumerator AsyncMethods_WithCallbacksAccessingDisposedObjects_HandleGracefully()
        {
            // Arrange
            var disposableObject = new MemoryStream();
            disposableObject.Dispose();

            Action<FileInfo[]> fileCallback = (files) =>
            {
                // Try to access disposed object
                disposableObject.WriteByte(0);
            };

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFilePanelAsync("Test", "", null, false, fileCallback);
            }, "Async methods should handle callbacks that access disposed objects");

            yield return new WaitForSeconds(0.1f);
        }

        #endregion

        #region Platform-Specific Error Tests

        /// <summary>
        /// Tests behavior when platform wrapper is not available.
        /// </summary>
        [Test]
        public void AllMethods_WhenPlatformNotSupported_HandleGracefully()
        {
            // Note: This test documents expected behavior on unsupported platforms
            // In actual implementation, unsupported platforms should throw NotSupportedException
            // during static initialization, not during method calls

            if (!TestUtilities.IsPlatformSupported())
            {
                // On unsupported platforms, we expect consistent behavior
                Assert.DoesNotThrow(() =>
                {
                    // The static constructor should have already thrown if platform is unsupported
                    // If we reach here, the platform is actually supported
                    var currentPlatform = TestUtilities.GetCurrentPlatform();
                    Debug.Log($"Platform {currentPlatform} is supported");
                }, "Platform support should be determined at static initialization");
            }
        }

        #endregion

        #region Memory and Resource Tests

        /// <summary>
        /// Tests methods under memory pressure conditions.
        /// </summary>
        [Test]
        public void AllMethods_UnderMemoryPressure_HandleGracefully()
        {
            // Simulate memory pressure by creating large objects
            var largeObjects = new byte[10][];
            
            try
            {
                for (int i = 0; i < largeObjects.Length; i++)
                {
                    largeObjects[i] = new byte[10 * 1024 * 1024]; // 10MB each
                }

                // Act & Assert
                Assert.DoesNotThrow(() =>
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel("Memory Test", "", null, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel("Memory Test", "", false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel("Memory Test", "", "test.txt", null);
                    
                    Assert.IsNotNull(fileResult, "File result should not be null under memory pressure");
                    Assert.IsNotNull(folderResult, "Folder result should not be null under memory pressure");
                    Assert.IsNotNull(saveResult, "Save result should not be null under memory pressure");
                }, "Methods should handle memory pressure gracefully");
            }
            finally
            {
                // Clean up large objects
                for (int i = 0; i < largeObjects.Length; i++)
                {
                    largeObjects[i] = null;
                }
                GC.Collect();
            }
        }

        /// <summary>
        /// Tests rapid successive method calls for race conditions.
        /// </summary>
        [Test]
        public void AllMethods_RapidSuccessiveCalls_HandleGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                for (int i = 0; i < 100; i++)
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel($"Rapid Test {i}", "", null, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel($"Rapid Test {i}", "", false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel($"Rapid Test {i}", "", $"test{i}.txt", null);
                    
                    Assert.IsNotNull(fileResult, $"File result should not be null on rapid call {i}");
                    Assert.IsNotNull(folderResult, $"Folder result should not be null on rapid call {i}");
                    Assert.IsNotNull(saveResult, $"Save result should not be null on rapid call {i}");
                }
            }, "Methods should handle rapid successive calls gracefully");
        }

        #endregion
    }
}
