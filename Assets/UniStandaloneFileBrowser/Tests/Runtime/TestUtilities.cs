using System;
using System.Collections.Generic;
using System.IO;
using USFB;

namespace USFB.Tests
{
    /// <summary>
    /// Provides common test data, utilities, and helper methods for USFB tests.
    /// </summary>
    public static class TestUtilities
    {
        #region Test Data Constants

        /// <summary>
        /// Standard test file paths for various scenarios.
        /// </summary>
        public static class TestPaths
        {
            public const string ValidFilePath = @"C:\TestFiles\document.txt";
            public const string ValidImagePath = @"C:\TestFiles\image.png";
            public const string ValidFolderPath = @"C:\TestFolder";
            public const string NonExistentPath = @"C:\NonExistent\path.txt";
            public const string EmptyPath = "";
            public const string WhitespacePath = "   ";
            public const string UnicodeFilePath = @"C:\TestFiles\测试文件.txt";
            public const string LongPath = @"C:\Very\Long\Path\That\Exceeds\Normal\Length\Limits\And\Contains\Many\Subdirectories\file.txt";
        }

        /// <summary>
        /// Standard test titles for dialog boxes.
        /// </summary>
        public static class TestTitles
        {
            public const string OpenFile = "Open File";
            public const string SaveFile = "Save File";
            public const string SelectFolder = "Select Folder";
            public const string EmptyTitle = "";
            public const string UnicodeTitle = "打开文件";
            public const string LongTitle = "This is a very long title that might cause issues with some dialog implementations";
        }

        /// <summary>
        /// Standard test file names.
        /// </summary>
        public static class TestFileNames
        {
            public const string Document = "document.txt";
            public const string Image = "image.png";
            public const string EmptyName = "";
            public const string UnicodeFileName = "测试文件.txt";
            public const string FileWithSpaces = "file with spaces.txt";
            public const string FileWithSpecialChars = "file@#$%^&().txt";
        }

        #endregion

        #region Extension Filter Test Data

        /// <summary>
        /// Creates a standard set of extension filters for testing.
        /// </summary>
        /// <returns>Array of common extension filters</returns>
        public static ExtensionFilter[] CreateStandardExtensionFilters()
        {
            return new[]
            {
                new ExtensionFilter("Text Files", "txt", "log"),
                new ExtensionFilter("Image Files", "png", "jpg", "jpeg", "gif"),
                new ExtensionFilter("All Files", "*")
            };
        }

        /// <summary>
        /// Creates extension filters with edge case scenarios.
        /// </summary>
        /// <returns>Array of edge case extension filters</returns>
        public static ExtensionFilter[] CreateEdgeCaseExtensionFilters()
        {
            return new[]
            {
                new ExtensionFilter("", "txt"), // Empty name
                new ExtensionFilter("No Extensions"), // No extensions
                new ExtensionFilter("Single Extension", "pdf"),
                new ExtensionFilter("Many Extensions", "doc", "docx", "rtf", "odt", "pages")
            };
        }

        /// <summary>
        /// Creates an empty extension filter array.
        /// </summary>
        /// <returns>Empty extension filter array</returns>
        public static ExtensionFilter[] CreateEmptyExtensionFilters()
        {
            return new ExtensionFilter[0];
        }

        /// <summary>
        /// Creates extension filters with null values for testing error handling.
        /// </summary>
        /// <returns>Null extension filter array</returns>
        public static ExtensionFilter[] CreateNullExtensionFilters()
        {
            return null;
        }

        #endregion

        #region File Path Utilities

        /// <summary>
        /// Creates a list of valid test file paths.
        /// </summary>
        /// <returns>List of valid file paths</returns>
        public static List<string> CreateValidFilePaths()
        {
            return new List<string>
            {
                TestPaths.ValidFilePath,
                TestPaths.ValidImagePath,
                @"C:\TestFiles\data.json",
                @"C:\TestFiles\config.xml"
            };
        }

        /// <summary>
        /// Creates a list of valid test folder paths.
        /// </summary>
        /// <returns>List of valid folder paths</returns>
        public static List<string> CreateValidFolderPaths()
        {
            return new List<string>
            {
                TestPaths.ValidFolderPath,
                @"C:\Documents",
                @"C:\Projects\Unity"
            };
        }

        /// <summary>
        /// Creates a list of invalid test paths for error testing.
        /// </summary>
        /// <returns>List of invalid paths</returns>
        public static List<string> CreateInvalidPaths()
        {
            return new List<string>
            {
                TestPaths.NonExistentPath,
                @"C:\Invalid<>Path\file.txt",
                @"C:\Path\With|Pipe\file.txt",
                @"",
                null
            };
        }

        #endregion

        #region Validation Helpers

        /// <summary>
        /// Validates that a FileInfo array contains expected file paths.
        /// </summary>
        /// <param name="fileInfos">FileInfo array to validate</param>
        /// <param name="expectedPaths">Expected file paths</param>
        /// <returns>True if validation passes</returns>
        public static bool ValidateFileInfoArray(FileInfo[] fileInfos, string[] expectedPaths)
        {
            if (fileInfos == null && expectedPaths == null)
                return true;

            if (fileInfos == null || expectedPaths == null)
                return false;

            if (fileInfos.Length != expectedPaths.Length)
                return false;

            for (int i = 0; i < fileInfos.Length; i++)
            {
                if (fileInfos[i] == null || fileInfos[i].FullName != expectedPaths[i])
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Validates that a DirectoryInfo array contains expected folder paths.
        /// </summary>
        /// <param name="directoryInfos">DirectoryInfo array to validate</param>
        /// <param name="expectedPaths">Expected folder paths</param>
        /// <returns>True if validation passes</returns>
        public static bool ValidateDirectoryInfoArray(DirectoryInfo[] directoryInfos, string[] expectedPaths)
        {
            if (directoryInfos == null && expectedPaths == null)
                return true;

            if (directoryInfos == null || expectedPaths == null)
                return false;

            if (directoryInfos.Length != expectedPaths.Length)
                return false;

            for (int i = 0; i < directoryInfos.Length; i++)
            {
                if (directoryInfos[i] == null || directoryInfos[i].FullName != expectedPaths[i])
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Validates that an ExtensionFilter has the expected properties.
        /// </summary>
        /// <param name="filter">ExtensionFilter to validate</param>
        /// <param name="expectedName">Expected name</param>
        /// <param name="expectedExtensions">Expected extensions</param>
        /// <returns>True if validation passes</returns>
        public static bool ValidateExtensionFilter(ExtensionFilter filter, string expectedName, string[] expectedExtensions)
        {
            if (filter.Name != expectedName)
                return false;

            if (filter.Extensions == null && expectedExtensions == null)
                return true;

            if (filter.Extensions == null || expectedExtensions == null)
                return false;

            if (filter.Extensions.Length != expectedExtensions.Length)
                return false;

            for (int i = 0; i < filter.Extensions.Length; i++)
            {
                if (filter.Extensions[i] != expectedExtensions[i])
                    return false;
            }

            return true;
        }

        #endregion

        #region Async Test Helpers

        /// <summary>
        /// Creates a callback action that captures results for async testing.
        /// </summary>
        /// <typeparam name="T">Type of the callback parameter</typeparam>
        /// <returns>Callback action and result container</returns>
        public static (Action<T> callback, AsyncTestResult<T> result) CreateAsyncCallback<T>()
        {
            var result = new AsyncTestResult<T>();
            Action<T> callback = (value) =>
            {
                result.Result = value;
                result.WasCalled = true;
                result.CallTime = DateTime.UtcNow;
            };
            return (callback, result);
        }

        /// <summary>
        /// Container for async test results.
        /// </summary>
        /// <typeparam name="T">Type of the result</typeparam>
        public class AsyncTestResult<T>
        {
            public T Result { get; set; }
            public bool WasCalled { get; set; }
            public DateTime CallTime { get; set; }
        }

        #endregion

        #region Platform Detection

        /// <summary>
        /// Determines if the current platform supports native file dialogs.
        /// </summary>
        /// <returns>True if platform supports native dialogs</returns>
        public static bool IsPlatformSupported()
        {
#if UNITY_EDITOR
            return true;
#elif UNITY_STANDALONE_WIN || UNITY_STANDALONE_OSX || UNITY_STANDALONE_LINUX
            return true;
#else
            return false;
#endif
        }

        /// <summary>
        /// Gets the current platform name for testing.
        /// </summary>
        /// <returns>Platform name string</returns>
        public static string GetCurrentPlatform()
        {
#if UNITY_EDITOR
            return "Editor";
#elif UNITY_STANDALONE_WIN
            return "Windows";
#elif UNITY_STANDALONE_OSX
            return "Mac";
#elif UNITY_STANDALONE_LINUX
            return "Linux";
#else
            return "Unsupported";
#endif
        }

        #endregion
    }
}
