using System;
using System.Collections;
using System.IO;
using System.Linq;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using USFB;
using USFB.Tests;
using USFB.Tests.Mocks;

namespace USFB.Tests
{
    /// <summary>
    /// Comprehensive unit tests for the StandaloneFileBrowser static class.
    /// Tests all public API methods, parameter validation, and integration scenarios.
    /// </summary>
    [TestFixture]
    public class StandaloneFileBrowserTests
    {
        private MockStandaloneFileBrowser _mockPlatform;

        #region Setup and Teardown

        /// <summary>
        /// Sets up test environment before each test.
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            _mockPlatform = new MockStandaloneFileBrowser();
            // Note: In a real implementation, we would need a way to inject the mock platform
            // For now, these tests document the expected behavior
        }

        /// <summary>
        /// Cleans up test environment after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            _mockPlatform?.Reset();
        }

        #endregion

        #region OpenFilePanel Tests

        /// <summary>
        /// Tests OpenFilePanel with valid parameters returns FileInfo array.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithValidParameters_ReturnsFileInfoArray()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act
            var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsInstanceOf<FileInfo[]>(result, "Result should be FileInfo array");
            
            // Note: In a real test environment with mock injection, we would verify:
            // - The mock was called with correct parameters
            // - The returned FileInfo objects have correct properties
        }

        /// <summary>
        /// Tests OpenFilePanel with null extensions parameter.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithNullExtensions_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            ExtensionFilter[] extensions = null;
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null even with null extensions");
            }, "OpenFilePanel should handle null extensions gracefully");
        }

        /// <summary>
        /// Tests OpenFilePanel with empty title parameter.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithEmptyTitle_HandlesGracefully()
        {
            // Arrange
            const string title = "";
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with empty title");
            }, "OpenFilePanel should handle empty title gracefully");
        }

        /// <summary>
        /// Tests OpenFilePanel with null title parameter.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithNullTitle_HandlesGracefully()
        {
            // Arrange
            const string title = null;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with null title");
            }, "OpenFilePanel should handle null title gracefully");
        }

        /// <summary>
        /// Tests OpenFilePanel with empty directory parameter.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithEmptyDirectory_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = "";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with empty directory");
            }, "OpenFilePanel should handle empty directory gracefully");
        }

        /// <summary>
        /// Tests OpenFilePanel with multiselect enabled.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithMultiselectEnabled_ReturnsMultipleFiles()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = true;

            // Act
            var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsInstanceOf<FileInfo[]>(result, "Result should be FileInfo array");
            
            // Note: With proper mock injection, we would verify that multiselect parameter
            // is passed correctly to the platform implementation
        }

        /// <summary>
        /// Tests OpenFilePanel with edge case extension filters.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithEdgeCaseExtensions_HandlesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateEdgeCaseExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with edge case extensions");
            }, "OpenFilePanel should handle edge case extensions gracefully");
        }

        #endregion

        #region OpenFilePanelAsync Tests

        /// <summary>
        /// Tests OpenFilePanelAsync with valid callback.
        /// </summary>
        [Test]
        public void OpenFilePanelAsync_WithValidCallback_InvokesCallback()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;
            
            var (callback, result) = TestUtilities.CreateAsyncCallback<FileInfo[]>();

            // Act
            StandaloneFileBrowser.OpenFilePanelAsync(title, directory, extensions, multiselect, callback);

            // Assert
            // Note: In a real test with proper async handling, we would wait for the callback
            // and verify that it was called with the expected parameters
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFilePanelAsync(title, directory, extensions, multiselect, callback);
            }, "OpenFilePanelAsync should not throw with valid parameters");
        }

        /// <summary>
        /// Tests OpenFilePanelAsync with null callback.
        /// </summary>
        [Test]
        public void OpenFilePanelAsync_WithNullCallback_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;
            Action<FileInfo[]> callback = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFilePanelAsync(title, directory, extensions, multiselect, callback);
            }, "OpenFilePanelAsync should handle null callback gracefully");
        }

        #endregion

        #region OpenFolderPanel Tests

        /// <summary>
        /// Tests OpenFolderPanel with valid parameters returns DirectoryInfo array.
        /// </summary>
        [Test]
        public void OpenFolderPanel_WithValidParameters_ReturnsDirectoryInfoArray()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = false;

            // Act
            var result = StandaloneFileBrowser.OpenFolderPanel(title, directory, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsInstanceOf<DirectoryInfo[]>(result, "Result should be DirectoryInfo array");
        }

        /// <summary>
        /// Tests OpenFolderPanel with multiselect enabled.
        /// </summary>
        [Test]
        public void OpenFolderPanel_WithMultiselectEnabled_ReturnsMultipleFolders()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = true;

            // Act
            var result = StandaloneFileBrowser.OpenFolderPanel(title, directory, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsInstanceOf<DirectoryInfo[]>(result, "Result should be DirectoryInfo array");
        }

        /// <summary>
        /// Tests OpenFolderPanel with null parameters.
        /// </summary>
        [Test]
        public void OpenFolderPanel_WithNullParameters_HandlesGracefully()
        {
            // Arrange
            const string title = null;
            const string directory = null;
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFolderPanel(title, directory, multiselect);
                Assert.IsNotNull(result, "Result should not be null with null parameters");
            }, "OpenFolderPanel should handle null parameters gracefully");
        }

        #endregion

        #region OpenFolderPanelAsync Tests

        /// <summary>
        /// Tests OpenFolderPanelAsync with valid callback.
        /// </summary>
        [Test]
        public void OpenFolderPanelAsync_WithValidCallback_InvokesCallback()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = false;
            
            var (callback, result) = TestUtilities.CreateAsyncCallback<DirectoryInfo[]>();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFolderPanelAsync(title, directory, multiselect, callback);
            }, "OpenFolderPanelAsync should not throw with valid parameters");
        }

        /// <summary>
        /// Tests OpenFolderPanelAsync with null callback.
        /// </summary>
        [Test]
        public void OpenFolderPanelAsync_WithNullCallback_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = false;
            Action<DirectoryInfo[]> callback = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.OpenFolderPanelAsync(title, directory, multiselect, callback);
            }, "OpenFolderPanelAsync should handle null callback gracefully");
        }

        #endregion

        #region SaveFilePanel Tests

        /// <summary>
        /// Tests SaveFilePanel with valid parameters returns file path.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithValidParameters_ReturnsFilePath()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string defaultName = TestUtilities.TestFileNames.Document;
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act
            var result = StandaloneFileBrowser.SaveFilePanel(title, directory, defaultName, extensions);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");
            Assert.IsInstanceOf<string>(result, "Result should be string");
        }

        /// <summary>
        /// Tests SaveFilePanel with null extensions.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithNullExtensions_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string defaultName = TestUtilities.TestFileNames.Document;
            ExtensionFilter[] extensions = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.SaveFilePanel(title, directory, defaultName, extensions);
                Assert.IsNotNull(result, "Result should not be null with null extensions");
            }, "SaveFilePanel should handle null extensions gracefully");
        }

        /// <summary>
        /// Tests SaveFilePanel with empty default name.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithEmptyDefaultName_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string defaultName = "";
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.SaveFilePanel(title, directory, defaultName, extensions);
                Assert.IsNotNull(result, "Result should not be null with empty default name");
            }, "SaveFilePanel should handle empty default name gracefully");
        }

        #endregion

        #region SaveFilePanelAsync Tests

        /// <summary>
        /// Tests SaveFilePanelAsync with valid callback.
        /// </summary>
        [Test]
        public void SaveFilePanelAsync_WithValidCallback_InvokesCallback()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string defaultName = TestUtilities.TestFileNames.Document;
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            
            var (callback, result) = TestUtilities.CreateAsyncCallback<string>();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.SaveFilePanelAsync(title, directory, defaultName, extensions, callback);
            }, "SaveFilePanelAsync should not throw with valid parameters");
        }

        /// <summary>
        /// Tests SaveFilePanelAsync with null callback.
        /// </summary>
        [Test]
        public void SaveFilePanelAsync_WithNullCallback_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string defaultName = TestUtilities.TestFileNames.Document;
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            Action<string> callback = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                StandaloneFileBrowser.SaveFilePanelAsync(title, directory, defaultName, extensions, callback);
            }, "SaveFilePanelAsync should handle null callback gracefully");
        }

        #endregion

        #region Platform Support Tests

        /// <summary>
        /// Tests that StandaloneFileBrowser initializes correctly on supported platforms.
        /// </summary>
        [Test]
        public void StandaloneFileBrowser_OnSupportedPlatform_InitializesCorrectly()
        {
            // Arrange & Act
            bool platformSupported = TestUtilities.IsPlatformSupported();
            string currentPlatform = TestUtilities.GetCurrentPlatform();

            // Assert
            if (platformSupported)
            {
                Assert.DoesNotThrow(() =>
                {
                    // Test that we can call the API without exceptions
                    var result = StandaloneFileBrowser.OpenFilePanel("Test", "", null, false);
                    Assert.IsNotNull(result, "Result should not be null on supported platform");
                }, $"StandaloneFileBrowser should work on supported platform: {currentPlatform}");
            }
            else
            {
                // On unsupported platforms, we expect a NotSupportedException during static initialization
                // This test documents the expected behavior
                Debug.Log($"Platform {currentPlatform} is not supported for native file dialogs");
            }
        }

        #endregion

        #region Unicode and Special Character Tests

        /// <summary>
        /// Tests OpenFilePanel with Unicode characters in title and directory.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithUnicodeCharacters_HandlesCorrectly()
        {
            // Arrange
            const string unicodeTitle = "打开文件 (Open File) 📁";
            const string unicodeDirectory = @"C:\测试文件夹";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(unicodeTitle, unicodeDirectory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with Unicode characters");
            }, "OpenFilePanel should handle Unicode characters gracefully");
        }

        /// <summary>
        /// Tests SaveFilePanel with Unicode default name.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithUnicodeDefaultName_HandlesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string unicodeDefaultName = "测试文档.txt";
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.SaveFilePanel(title, directory, unicodeDefaultName, extensions);
                Assert.IsNotNull(result, "Result should not be null with Unicode default name");
            }, "SaveFilePanel should handle Unicode default name gracefully");
        }

        #endregion

        #region Performance and Stress Tests

        /// <summary>
        /// Tests OpenFilePanel with large number of extension filters.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithManyExtensionFilters_HandlesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            const bool multiselect = false;

            // Create a large number of extension filters
            var extensions = new ExtensionFilter[100];
            for (int i = 0; i < extensions.Length; i++)
            {
                extensions[i] = new ExtensionFilter($"Filter {i}", $"ext{i}");
            }

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with many extension filters");
            }, "OpenFilePanel should handle large number of extension filters");
        }

        /// <summary>
        /// Tests repeated calls to OpenFilePanel for memory leaks.
        /// </summary>
        [Test]
        public void OpenFilePanel_RepeatedCalls_DoesNotLeakMemory()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                for (int i = 0; i < 10; i++)
                {
                    var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                    Assert.IsNotNull(result, $"Result should not be null on iteration {i}");
                }
            }, "Repeated OpenFilePanel calls should not cause memory issues");
        }

        #endregion

        #region Error Handling Tests

        /// <summary>
        /// Tests behavior when all parameters are null.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithAllNullParameters_HandlesGracefully()
        {
            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(null, null, null, false);
                Assert.IsNotNull(result, "Result should not be null even with all null parameters");
            }, "OpenFilePanel should handle all null parameters gracefully");
        }

        /// <summary>
        /// Tests behavior when directory contains invalid path characters.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithInvalidPathCharacters_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string invalidDirectory = @"C:\Invalid<>Path|With*Chars";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel(title, invalidDirectory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with invalid path characters");
            }, "OpenFilePanel should handle invalid path characters gracefully");
        }

        /// <summary>
        /// Tests SaveFilePanel with invalid default name characters.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithInvalidDefaultNameCharacters_HandlesGracefully()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\TestFiles";
            const string invalidDefaultName = "file<>name|with*invalid:chars.txt";
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.SaveFilePanel(title, directory, invalidDefaultName, extensions);
                Assert.IsNotNull(result, "Result should not be null with invalid default name characters");
            }, "SaveFilePanel should handle invalid default name characters gracefully");
        }

        #endregion

        #region FileInfo and DirectoryInfo Validation Tests

        /// <summary>
        /// Tests that returned FileInfo objects have correct properties.
        /// </summary>
        [Test]
        public void OpenFilePanel_ReturnedFileInfos_HaveCorrectProperties()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act
            var result = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");

            foreach (var fileInfo in result)
            {
                if (fileInfo != null)
                {
                    Assert.IsNotNull(fileInfo.FullName, "FileInfo.FullName should not be null");
                    Assert.IsNotEmpty(fileInfo.FullName, "FileInfo.FullName should not be empty");
                    Assert.IsInstanceOf<FileInfo>(fileInfo, "Each item should be a FileInfo object");
                }
            }
        }

        /// <summary>
        /// Tests that returned DirectoryInfo objects have correct properties.
        /// </summary>
        [Test]
        public void OpenFolderPanel_ReturnedDirectoryInfos_HaveCorrectProperties()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = false;

            // Act
            var result = StandaloneFileBrowser.OpenFolderPanel(title, directory, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null");

            foreach (var directoryInfo in result)
            {
                if (directoryInfo != null)
                {
                    Assert.IsNotNull(directoryInfo.FullName, "DirectoryInfo.FullName should not be null");
                    Assert.IsNotEmpty(directoryInfo.FullName, "DirectoryInfo.FullName should not be empty");
                    Assert.IsInstanceOf<DirectoryInfo>(directoryInfo, "Each item should be a DirectoryInfo object");
                }
            }
        }

        #endregion

        #region Integration with TestUtilities

        /// <summary>
        /// Tests that TestUtilities validation helpers work with actual API results.
        /// </summary>
        [Test]
        public void TestUtilities_ValidationHelpers_WorkWithAPIResults()
        {
            // Arrange
            var validPaths = TestUtilities.CreateValidFilePaths().ToArray();
            var validFolderPaths = TestUtilities.CreateValidFolderPaths().ToArray();

            // Act
            var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", "", null, true);
            var folderResult = StandaloneFileBrowser.OpenFolderPanel("Test", "", true);

            // Assert
            Assert.DoesNotThrow(() =>
            {
                // These validation methods should work with actual API results
                bool fileValidation = TestUtilities.ValidateFileInfoArray(fileResult, new string[0]);
                bool folderValidation = TestUtilities.ValidateDirectoryInfoArray(folderResult, new string[0]);

                // The validation itself may return false (since we're not controlling the results),
                // but the methods should not throw exceptions
                Assert.IsTrue(fileValidation || !fileValidation, "File validation should complete without error");
                Assert.IsTrue(folderValidation || !folderValidation, "Folder validation should complete without error");
            }, "TestUtilities validation helpers should work with API results");
        }

        #endregion
    }
}
