using System;
using System.Collections.Generic;
using USFB;

namespace USFB.Tests.Mocks
{
    /// <summary>
    /// Mock implementation of IStandaloneFileBrowser for testing purposes.
    /// Provides configurable behavior for simulating various dialog scenarios.
    /// </summary>
    public class MockStandaloneFileBrowser : IStandaloneFileBrowser
    {
        private readonly List<string> _mockFilePaths;
        private readonly List<string> _mockFolderPaths;
        private readonly string _mockSaveFilePath;
        private readonly bool _shouldReturnEmpty;
        private readonly bool _shouldThrowException;
        private readonly Exception _exceptionToThrow;

        /// <summary>
        /// Gets the last parameters passed to OpenFilePanel method for verification.
        /// </summary>
        public OpenFilePanelParams LastOpenFilePanelParams { get; private set; }

        /// <summary>
        /// Gets the last parameters passed to OpenFolderPanel method for verification.
        /// </summary>
        public OpenFolderPanelParams LastOpenFolderPanelParams { get; private set; }

        /// <summary>
        /// Gets the last parameters passed to SaveFilePanel method for verification.
        /// </summary>
        public SaveFilePanelParams LastSaveFilePanelParams { get; private set; }

        /// <summary>
        /// Gets the number of times each method was called.
        /// </summary>
        public MethodCallCounts CallCounts { get; private set; }

        /// <summary>
        /// Initializes a new instance of MockStandaloneFileBrowser with default successful behavior.
        /// </summary>
        /// <param name="mockFilePaths">File paths to return from OpenFilePanel calls</param>
        /// <param name="mockFolderPaths">Folder paths to return from OpenFolderPanel calls</param>
        /// <param name="mockSaveFilePath">File path to return from SaveFilePanel calls</param>
        public MockStandaloneFileBrowser(
            List<string> mockFilePaths = null,
            List<string> mockFolderPaths = null,
            string mockSaveFilePath = null)
        {
            _mockFilePaths = mockFilePaths ?? new List<string> { @"C:\TestFiles\test.txt" };
            _mockFolderPaths = mockFolderPaths ?? new List<string> { @"C:\TestFolder" };
            _mockSaveFilePath = mockSaveFilePath ?? @"C:\TestFiles\saved.txt";
            _shouldReturnEmpty = false;
            _shouldThrowException = false;
            CallCounts = new MethodCallCounts();
        }

        /// <summary>
        /// Initializes a new instance of MockStandaloneFileBrowser with configurable behavior.
        /// </summary>
        /// <param name="shouldReturnEmpty">If true, returns empty arrays/null strings</param>
        /// <param name="shouldThrowException">If true, throws the specified exception</param>
        /// <param name="exceptionToThrow">Exception to throw when shouldThrowException is true</param>
        public MockStandaloneFileBrowser(
            bool shouldReturnEmpty,
            bool shouldThrowException = false,
            Exception exceptionToThrow = null)
        {
            _mockFilePaths = new List<string>();
            _mockFolderPaths = new List<string>();
            _mockSaveFilePath = string.Empty;
            _shouldReturnEmpty = shouldReturnEmpty;
            _shouldThrowException = shouldThrowException;
            _exceptionToThrow = exceptionToThrow ?? new InvalidOperationException("Mock exception");
            CallCounts = new MethodCallCounts();
        }

        /// <summary>
        /// Mock implementation of OpenFilePanel.
        /// </summary>
        public string[] OpenFilePanel(string title, string directory, ExtensionFilter[] extensions, bool multiselect)
        {
            CallCounts.OpenFilePanel++;
            LastOpenFilePanelParams = new OpenFilePanelParams(title, directory, extensions, multiselect);

            if (_shouldThrowException)
                throw _exceptionToThrow;

            if (_shouldReturnEmpty)
                return new string[0];

            return multiselect ? _mockFilePaths.ToArray() : new[] { _mockFilePaths[0] };
        }

        /// <summary>
        /// Mock implementation of OpenFolderPanel.
        /// </summary>
        public string[] OpenFolderPanel(string title, string directory, bool multiselect)
        {
            CallCounts.OpenFolderPanel++;
            LastOpenFolderPanelParams = new OpenFolderPanelParams(title, directory, multiselect);

            if (_shouldThrowException)
                throw _exceptionToThrow;

            if (_shouldReturnEmpty)
                return new string[0];

            return multiselect ? _mockFolderPaths.ToArray() : new[] { _mockFolderPaths[0] };
        }

        /// <summary>
        /// Mock implementation of SaveFilePanel.
        /// </summary>
        public string SaveFilePanel(string title, string directory, string defaultName, ExtensionFilter[] extensions)
        {
            CallCounts.SaveFilePanel++;
            LastSaveFilePanelParams = new SaveFilePanelParams(title, directory, defaultName, extensions);

            if (_shouldThrowException)
                throw _exceptionToThrow;

            if (_shouldReturnEmpty)
                return string.Empty;

            return _mockSaveFilePath;
        }

        /// <summary>
        /// Mock implementation of OpenFilePanelAsync.
        /// </summary>
        public void OpenFilePanelAsync(string title, string directory, ExtensionFilter[] extensions, bool multiselect, Action<string[]> cb)
        {
            CallCounts.OpenFilePanelAsync++;
            LastOpenFilePanelParams = new OpenFilePanelParams(title, directory, extensions, multiselect);

            if (_shouldThrowException)
            {
                cb?.Invoke(null);
                return;
            }

            var result = _shouldReturnEmpty ? new string[0] : 
                         multiselect ? _mockFilePaths.ToArray() : new[] { _mockFilePaths[0] };
            cb?.Invoke(result);
        }

        /// <summary>
        /// Mock implementation of OpenFolderPanelAsync.
        /// </summary>
        public void OpenFolderPanelAsync(string title, string directory, bool multiselect, Action<string[]> cb)
        {
            CallCounts.OpenFolderPanelAsync++;
            LastOpenFolderPanelParams = new OpenFolderPanelParams(title, directory, multiselect);

            if (_shouldThrowException)
            {
                cb?.Invoke(null);
                return;
            }

            var result = _shouldReturnEmpty ? new string[0] : 
                         multiselect ? _mockFolderPaths.ToArray() : new[] { _mockFolderPaths[0] };
            cb?.Invoke(result);
        }

        /// <summary>
        /// Mock implementation of SaveFilePanelAsync.
        /// </summary>
        public void SaveFilePanelAsync(string title, string directory, string defaultName, ExtensionFilter[] extensions, Action<string> cb)
        {
            CallCounts.SaveFilePanelAsync++;
            LastSaveFilePanelParams = new SaveFilePanelParams(title, directory, defaultName, extensions);

            if (_shouldThrowException)
            {
                cb?.Invoke(null);
                return;
            }

            var result = _shouldReturnEmpty ? string.Empty : _mockSaveFilePath;
            cb?.Invoke(result);
        }

        /// <summary>
        /// Resets all call counts and stored parameters.
        /// </summary>
        public void Reset()
        {
            CallCounts = new MethodCallCounts();
            LastOpenFilePanelParams = null;
            LastOpenFolderPanelParams = null;
            LastSaveFilePanelParams = null;
        }
    }

    /// <summary>
    /// Tracks the number of times each method was called.
    /// </summary>
    public class MethodCallCounts
    {
        public int OpenFilePanel { get; set; }
        public int OpenFolderPanel { get; set; }
        public int SaveFilePanel { get; set; }
        public int OpenFilePanelAsync { get; set; }
        public int OpenFolderPanelAsync { get; set; }
        public int SaveFilePanelAsync { get; set; }
    }

    /// <summary>
    /// Stores parameters passed to OpenFilePanel method calls.
    /// </summary>
    public class OpenFilePanelParams
    {
        public string Title { get; }
        public string Directory { get; }
        public ExtensionFilter[] Extensions { get; }
        public bool Multiselect { get; }

        public OpenFilePanelParams(string title, string directory, ExtensionFilter[] extensions, bool multiselect)
        {
            Title = title;
            Directory = directory;
            Extensions = extensions;
            Multiselect = multiselect;
        }
    }

    /// <summary>
    /// Stores parameters passed to OpenFolderPanel method calls.
    /// </summary>
    public class OpenFolderPanelParams
    {
        public string Title { get; }
        public string Directory { get; }
        public bool Multiselect { get; }

        public OpenFolderPanelParams(string title, string directory, bool multiselect)
        {
            Title = title;
            Directory = directory;
            Multiselect = multiselect;
        }
    }

    /// <summary>
    /// Stores parameters passed to SaveFilePanel method calls.
    /// </summary>
    public class SaveFilePanelParams
    {
        public string Title { get; }
        public string Directory { get; }
        public string DefaultName { get; }
        public ExtensionFilter[] Extensions { get; }

        public SaveFilePanelParams(string title, string directory, string defaultName, ExtensionFilter[] extensions)
        {
            Title = title;
            Directory = directory;
            DefaultName = defaultName;
            Extensions = extensions;
        }
    }
}
