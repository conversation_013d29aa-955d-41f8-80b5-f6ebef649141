using System;
using NUnit.Framework;
using USFB;
using USFB.Tests;

namespace USFB.Tests
{
    /// <summary>
    /// Comprehensive unit tests for the ExtensionFilter struct.
    /// Tests constructor behavior, property validation, and edge cases.
    /// </summary>
    [TestFixture]
    public class ExtensionFilterTests
    {
        #region Constructor Tests

        /// <summary>
        /// Tests ExtensionFilter constructor with valid name and single extension.
        /// </summary>
        [Test]
        public void Constructor_WithValidNameAndSingleExtension_SetsPropertiesCorrectly()
        {
            // Arrange
            const string expectedName = "Text Files";
            const string expectedExtension = "txt";

            // Act
            var filter = new ExtensionFilter(expectedName, expectedExtension);

            // Assert
            Assert.AreEqual(expectedName, filter.Name, "Name property should match constructor parameter");
            Assert.IsNotNull(filter.Extensions, "Extensions array should not be null");
            Assert.AreEqual(1, filter.Extensions.Length, "Extensions array should contain one element");
            Assert.AreEqual(expectedExtension, filter.Extensions[0], "Extension should match constructor parameter");
        }

        /// <summary>
        /// Tests ExtensionFilter constructor with valid name and multiple extensions.
        /// </summary>
        [Test]
        public void Constructor_WithValidNameAndMultipleExtensions_SetsPropertiesCorrectly()
        {
            // Arrange
            const string expectedName = "Image Files";
            string[] expectedExtensions = { "png", "jpg", "jpeg", "gif" };

            // Act
            var filter = new ExtensionFilter(expectedName, expectedExtensions);

            // Assert
            Assert.AreEqual(expectedName, filter.Name, "Name property should match constructor parameter");
            Assert.IsNotNull(filter.Extensions, "Extensions array should not be null");
            Assert.AreEqual(expectedExtensions.Length, filter.Extensions.Length, "Extensions array length should match");
            
            for (int i = 0; i < expectedExtensions.Length; i++)
            {
                Assert.AreEqual(expectedExtensions[i], filter.Extensions[i], 
                    $"Extension at index {i} should match constructor parameter");
            }
        }

        /// <summary>
        /// Tests ExtensionFilter constructor with empty name.
        /// </summary>
        [Test]
        public void Constructor_WithEmptyName_SetsNameToEmptyString()
        {
            // Arrange
            const string emptyName = "";
            const string extension = "txt";

            // Act
            var filter = new ExtensionFilter(emptyName, extension);

            // Assert
            Assert.AreEqual(emptyName, filter.Name, "Name should be empty string");
            Assert.IsNotNull(filter.Extensions, "Extensions array should not be null");
            Assert.AreEqual(1, filter.Extensions.Length, "Extensions array should contain one element");
            Assert.AreEqual(extension, filter.Extensions[0], "Extension should be preserved");
        }

        /// <summary>
        /// Tests ExtensionFilter constructor with null name.
        /// </summary>
        [Test]
        public void Constructor_WithNullName_SetsNameToNull()
        {
            // Arrange
            const string nullName = null;
            const string extension = "txt";

            // Act
            var filter = new ExtensionFilter(nullName, extension);

            // Assert
            Assert.IsNull(filter.Name, "Name should be null");
            Assert.IsNotNull(filter.Extensions, "Extensions array should not be null");
            Assert.AreEqual(1, filter.Extensions.Length, "Extensions array should contain one element");
            Assert.AreEqual(extension, filter.Extensions[0], "Extension should be preserved");
        }

        /// <summary>
        /// Tests ExtensionFilter constructor with no extensions.
        /// </summary>
        [Test]
        public void Constructor_WithNoExtensions_CreatesEmptyExtensionsArray()
        {
            // Arrange
            const string name = "No Extensions";

            // Act
            var filter = new ExtensionFilter(name);

            // Assert
            Assert.AreEqual(name, filter.Name, "Name property should match constructor parameter");
            Assert.IsNotNull(filter.Extensions, "Extensions array should not be null");
            Assert.AreEqual(0, filter.Extensions.Length, "Extensions array should be empty");
        }

        /// <summary>
        /// Tests ExtensionFilter constructor with null extensions array.
        /// </summary>
        [Test]
        public void Constructor_WithNullExtensionsArray_HandlesGracefully()
        {
            // Arrange
            const string name = "Test Filter";
            string[] nullExtensions = null;

            // Act
            var filter = new ExtensionFilter(name, nullExtensions);

            // Assert
            Assert.AreEqual(name, filter.Name, "Name property should match constructor parameter");
            // Note: The behavior with null extensions depends on the implementation
            // This test documents the current behavior
        }

        #endregion

        #region Property Tests

        /// <summary>
        /// Tests that Name property can be accessed and modified.
        /// </summary>
        [Test]
        public void Name_Property_CanBeAccessedAndModified()
        {
            // Arrange
            var filter = new ExtensionFilter("Original Name", "txt");
            const string newName = "Modified Name";

            // Act
            filter.Name = newName;

            // Assert
            Assert.AreEqual(newName, filter.Name, "Name property should be modifiable");
        }

        /// <summary>
        /// Tests that Extensions property can be accessed and modified.
        /// </summary>
        [Test]
        public void Extensions_Property_CanBeAccessedAndModified()
        {
            // Arrange
            var filter = new ExtensionFilter("Test", "txt");
            string[] newExtensions = { "doc", "docx" };

            // Act
            filter.Extensions = newExtensions;

            // Assert
            Assert.AreEqual(newExtensions, filter.Extensions, "Extensions property should be modifiable");
            Assert.AreEqual(2, filter.Extensions.Length, "Extensions array length should match new array");
            Assert.AreEqual("doc", filter.Extensions[0], "First extension should match");
            Assert.AreEqual("docx", filter.Extensions[1], "Second extension should match");
        }

        #endregion

        #region Edge Case Tests

        /// <summary>
        /// Tests ExtensionFilter with very long name.
        /// </summary>
        [Test]
        public void Constructor_WithVeryLongName_HandlesCorrectly()
        {
            // Arrange
            string longName = new string('A', 1000); // 1000 character name
            const string extension = "txt";

            // Act
            var filter = new ExtensionFilter(longName, extension);

            // Assert
            Assert.AreEqual(longName, filter.Name, "Long name should be preserved");
            Assert.AreEqual(1, filter.Extensions.Length, "Extensions should be preserved");
        }

        /// <summary>
        /// Tests ExtensionFilter with Unicode characters in name.
        /// </summary>
        [Test]
        public void Constructor_WithUnicodeName_HandlesCorrectly()
        {
            // Arrange
            const string unicodeName = "文本文件 (Text Files) 📄";
            const string extension = "txt";

            // Act
            var filter = new ExtensionFilter(unicodeName, extension);

            // Assert
            Assert.AreEqual(unicodeName, filter.Name, "Unicode name should be preserved");
            Assert.AreEqual(extension, filter.Extensions[0], "Extension should be preserved");
        }

        /// <summary>
        /// Tests ExtensionFilter with special characters in extensions.
        /// </summary>
        [Test]
        public void Constructor_WithSpecialCharacterExtensions_HandlesCorrectly()
        {
            // Arrange
            const string name = "Special Files";
            string[] specialExtensions = { "file.ext", "name-with-dash", "name_with_underscore" };

            // Act
            var filter = new ExtensionFilter(name, specialExtensions);

            // Assert
            Assert.AreEqual(name, filter.Name, "Name should be preserved");
            Assert.AreEqual(specialExtensions.Length, filter.Extensions.Length, "All extensions should be preserved");
            
            for (int i = 0; i < specialExtensions.Length; i++)
            {
                Assert.AreEqual(specialExtensions[i], filter.Extensions[i], 
                    $"Special extension at index {i} should be preserved");
            }
        }

        /// <summary>
        /// Tests ExtensionFilter with empty string extensions.
        /// </summary>
        [Test]
        public void Constructor_WithEmptyStringExtensions_HandlesCorrectly()
        {
            // Arrange
            const string name = "Empty Extensions";
            string[] emptyExtensions = { "", "txt", "" };

            // Act
            var filter = new ExtensionFilter(name, emptyExtensions);

            // Assert
            Assert.AreEqual(name, filter.Name, "Name should be preserved");
            Assert.AreEqual(emptyExtensions.Length, filter.Extensions.Length, "All extensions should be preserved");
            Assert.AreEqual("", filter.Extensions[0], "Empty extension should be preserved");
            Assert.AreEqual("txt", filter.Extensions[1], "Valid extension should be preserved");
            Assert.AreEqual("", filter.Extensions[2], "Empty extension should be preserved");
        }

        /// <summary>
        /// Tests ExtensionFilter with duplicate extensions.
        /// </summary>
        [Test]
        public void Constructor_WithDuplicateExtensions_PreservesDuplicates()
        {
            // Arrange
            const string name = "Duplicate Extensions";
            string[] duplicateExtensions = { "txt", "doc", "txt", "pdf", "doc" };

            // Act
            var filter = new ExtensionFilter(name, duplicateExtensions);

            // Assert
            Assert.AreEqual(name, filter.Name, "Name should be preserved");
            Assert.AreEqual(duplicateExtensions.Length, filter.Extensions.Length, "All extensions including duplicates should be preserved");
            
            for (int i = 0; i < duplicateExtensions.Length; i++)
            {
                Assert.AreEqual(duplicateExtensions[i], filter.Extensions[i], 
                    $"Extension at index {i} should match original including duplicates");
            }
        }

        #endregion

        #region Validation Helper Tests

        /// <summary>
        /// Tests the TestUtilities.ValidateExtensionFilter helper method.
        /// </summary>
        [Test]
        public void ValidateExtensionFilter_WithMatchingFilter_ReturnsTrue()
        {
            // Arrange
            var filter = new ExtensionFilter("Test Files", "txt", "log");
            string[] expectedExtensions = { "txt", "log" };

            // Act
            bool isValid = TestUtilities.ValidateExtensionFilter(filter, "Test Files", expectedExtensions);

            // Assert
            Assert.IsTrue(isValid, "ValidateExtensionFilter should return true for matching filter");
        }

        /// <summary>
        /// Tests the TestUtilities.ValidateExtensionFilter helper method with mismatched name.
        /// </summary>
        [Test]
        public void ValidateExtensionFilter_WithMismatchedName_ReturnsFalse()
        {
            // Arrange
            var filter = new ExtensionFilter("Test Files", "txt");
            string[] expectedExtensions = { "txt" };

            // Act
            bool isValid = TestUtilities.ValidateExtensionFilter(filter, "Different Name", expectedExtensions);

            // Assert
            Assert.IsFalse(isValid, "ValidateExtensionFilter should return false for mismatched name");
        }

        /// <summary>
        /// Tests the TestUtilities.ValidateExtensionFilter helper method with mismatched extensions.
        /// </summary>
        [Test]
        public void ValidateExtensionFilter_WithMismatchedExtensions_ReturnsFalse()
        {
            // Arrange
            var filter = new ExtensionFilter("Test Files", "txt", "log");
            string[] expectedExtensions = { "txt", "doc" };

            // Act
            bool isValid = TestUtilities.ValidateExtensionFilter(filter, "Test Files", expectedExtensions);

            // Assert
            Assert.IsFalse(isValid, "ValidateExtensionFilter should return false for mismatched extensions");
        }

        #endregion

        #region Integration with TestUtilities

        /// <summary>
        /// Tests that standard extension filters from TestUtilities work correctly.
        /// </summary>
        [Test]
        public void StandardExtensionFilters_FromTestUtilities_AreValid()
        {
            // Act
            var standardFilters = TestUtilities.CreateStandardExtensionFilters();

            // Assert
            Assert.IsNotNull(standardFilters, "Standard filters should not be null");
            Assert.Greater(standardFilters.Length, 0, "Standard filters should contain elements");
            
            foreach (var filter in standardFilters)
            {
                Assert.IsNotNull(filter.Name, "Each filter should have a name");
                Assert.IsNotNull(filter.Extensions, "Each filter should have extensions array");
            }
        }

        /// <summary>
        /// Tests that edge case extension filters from TestUtilities work correctly.
        /// </summary>
        [Test]
        public void EdgeCaseExtensionFilters_FromTestUtilities_AreValid()
        {
            // Act
            var edgeCaseFilters = TestUtilities.CreateEdgeCaseExtensionFilters();

            // Assert
            Assert.IsNotNull(edgeCaseFilters, "Edge case filters should not be null");
            Assert.Greater(edgeCaseFilters.Length, 0, "Edge case filters should contain elements");
            
            // Verify that edge cases are properly represented
            bool hasEmptyName = false;
            bool hasNoExtensions = false;
            
            foreach (var filter in edgeCaseFilters)
            {
                if (string.IsNullOrEmpty(filter.Name))
                    hasEmptyName = true;
                if (filter.Extensions == null || filter.Extensions.Length == 0)
                    hasNoExtensions = true;
            }
            
            Assert.IsTrue(hasEmptyName, "Edge case filters should include empty name scenario");
            Assert.IsTrue(hasNoExtensions, "Edge case filters should include no extensions scenario");
        }

        #endregion
    }
}
