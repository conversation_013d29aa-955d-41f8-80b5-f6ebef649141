using System;
using System.Collections;
using System.IO;
using System.Linq;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using USFB;
using USFB.Tests;
using USFB.Tests.Mocks;

namespace USFB.Tests
{
    /// <summary>
    /// Integration tests that verify complete file browser workflows.
    /// These tests simulate real-world usage scenarios and end-to-end functionality.
    /// </summary>
    [TestFixture]
    public class IntegrationTests
    {
        private MockStandaloneFileBrowser _mockBrowser;

        #region Setup and Teardown

        /// <summary>
        /// Sets up test environment before each test.
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            _mockBrowser = new MockStandaloneFileBrowser();
        }

        /// <summary>
        /// Cleans up test environment after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            _mockBrowser?.Reset();
        }

        #endregion

        #region Complete File Selection Workflow Tests

        /// <summary>
        /// Tests complete workflow: Open file dialog → Select files → Process FileInfo objects.
        /// </summary>
        [Test]
        public void CompleteFileSelectionWorkflow_WithValidSelection_ProcessesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = true;

            // Act
            var selectedFiles = StandaloneFileBrowser.OpenFilePanel(title, directory, extensions, multiselect);

            // Assert
            Assert.IsNotNull(selectedFiles, "Selected files should not be null");
            Assert.IsInstanceOf<FileInfo[]>(selectedFiles, "Result should be FileInfo array");

            // Verify FileInfo objects are properly constructed
            foreach (var fileInfo in selectedFiles)
            {
                if (fileInfo != null)
                {
                    Assert.IsNotNull(fileInfo.FullName, "FileInfo.FullName should not be null");
                    Assert.IsNotEmpty(fileInfo.FullName, "FileInfo.FullName should not be empty");
                    Assert.IsInstanceOf<FileInfo>(fileInfo, "Each item should be a FileInfo object");
                    
                    // Verify that FileInfo properties are accessible
                    Assert.DoesNotThrow(() =>
                    {
                        var name = fileInfo.Name;
                        var directory = fileInfo.DirectoryName;
                        var extension = fileInfo.Extension;
                        
                        Assert.IsNotNull(name, "FileInfo.Name should be accessible");
                    }, "FileInfo properties should be accessible");
                }
            }
        }

        /// <summary>
        /// Tests complete workflow with extension filtering applied.
        /// </summary>
        [Test]
        public void CompleteFileSelectionWorkflow_WithExtensionFiltering_AppliesFiltersCorrectly()
        {
            // Arrange
            const string title = "Select Image Files";
            const string directory = @"C:\Images";
            var imageExtensions = new[]
            {
                new ExtensionFilter("Image Files", "png", "jpg", "jpeg", "gif", "bmp"),
                new ExtensionFilter("PNG Files", "png"),
                new ExtensionFilter("JPEG Files", "jpg", "jpeg")
            };
            const bool multiselect = true;

            // Act
            var selectedFiles = StandaloneFileBrowser.OpenFilePanel(title, directory, imageExtensions, multiselect);

            // Assert
            Assert.IsNotNull(selectedFiles, "Selected files should not be null");
            
            // In a real scenario with actual file selection, we would verify that only files
            // matching the specified extensions are returned. Here we document the expected behavior.
            foreach (var fileInfo in selectedFiles)
            {
                if (fileInfo != null && !string.IsNullOrEmpty(fileInfo.Extension))
                {
                    var extension = fileInfo.Extension.TrimStart('.');
                    var allowedExtensions = imageExtensions.SelectMany(f => f.Extensions).ToArray();
                    
                    // Note: This assertion would be meaningful with actual file selection
                    // Assert.Contains(extension.ToLower(), allowedExtensions.Select(e => e.ToLower()).ToArray(),
                    //     "Selected file should match one of the specified extensions");
                }
            }
        }

        #endregion

        #region Complete Folder Selection Workflow Tests

        /// <summary>
        /// Tests complete workflow: Open folder dialog → Select folders → Process DirectoryInfo objects.
        /// </summary>
        [Test]
        public void CompleteFolderSelectionWorkflow_WithValidSelection_ProcessesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = true;

            // Act
            var selectedFolders = StandaloneFileBrowser.OpenFolderPanel(title, directory, multiselect);

            // Assert
            Assert.IsNotNull(selectedFolders, "Selected folders should not be null");
            Assert.IsInstanceOf<DirectoryInfo[]>(selectedFolders, "Result should be DirectoryInfo array");

            // Verify DirectoryInfo objects are properly constructed
            foreach (var directoryInfo in selectedFolders)
            {
                if (directoryInfo != null)
                {
                    Assert.IsNotNull(directoryInfo.FullName, "DirectoryInfo.FullName should not be null");
                    Assert.IsNotEmpty(directoryInfo.FullName, "DirectoryInfo.FullName should not be empty");
                    Assert.IsInstanceOf<DirectoryInfo>(directoryInfo, "Each item should be a DirectoryInfo object");
                    
                    // Verify that DirectoryInfo properties are accessible
                    Assert.DoesNotThrow(() =>
                    {
                        var name = directoryInfo.Name;
                        var parent = directoryInfo.Parent;
                        var root = directoryInfo.Root;
                        
                        Assert.IsNotNull(name, "DirectoryInfo.Name should be accessible");
                    }, "DirectoryInfo properties should be accessible");
                }
            }
        }

        /// <summary>
        /// Tests folder selection workflow with single selection mode.
        /// </summary>
        [Test]
        public void CompleteFolderSelectionWorkflow_WithSingleSelection_ReturnsOneFolder()
        {
            // Arrange
            const string title = "Select Single Folder";
            const string directory = @"C:\Projects";
            const bool multiselect = false;

            // Act
            var selectedFolders = StandaloneFileBrowser.OpenFolderPanel(title, directory, multiselect);

            // Assert
            Assert.IsNotNull(selectedFolders, "Selected folders should not be null");
            
            // In single selection mode, we expect at most one folder
            // (could be zero if user cancels)
            Assert.LessOrEqual(selectedFolders.Length, 1, 
                "Single selection mode should return at most one folder");
        }

        #endregion

        #region Complete Save File Workflow Tests

        /// <summary>
        /// Tests complete workflow: Save file dialog → Specify filename → Get save path.
        /// </summary>
        [Test]
        public void CompleteSaveFileWorkflow_WithValidInput_ReturnsValidPath()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\Documents";
            const string defaultName = "MyDocument.txt";
            var extensions = new[]
            {
                new ExtensionFilter("Text Files", "txt"),
                new ExtensionFilter("Rich Text", "rtf"),
                new ExtensionFilter("All Files", "*")
            };

            // Act
            var savePath = StandaloneFileBrowser.SaveFilePanel(title, directory, defaultName, extensions);

            // Assert
            Assert.IsNotNull(savePath, "Save path should not be null");
            Assert.IsInstanceOf<string>(savePath, "Save path should be string");
            
            // In a real scenario, we would verify the path format and extension
            if (!string.IsNullOrEmpty(savePath))
            {
                Assert.DoesNotThrow(() =>
                {
                    var pathInfo = new FileInfo(savePath);
                    // Path should be valid for FileInfo construction
                    Assert.IsNotNull(pathInfo.DirectoryName, "Save path should have valid directory");
                }, "Save path should be valid for file operations");
            }
        }

        /// <summary>
        /// Tests save file workflow with automatic extension addition.
        /// </summary>
        [Test]
        public void CompleteSaveFileWorkflow_WithExtensionFilters_HandlesExtensionsCorrectly()
        {
            // Arrange
            const string title = "Save Document";
            const string directory = @"C:\Documents";
            const string defaultName = "document"; // No extension
            var extensions = new[]
            {
                new ExtensionFilter("Word Documents", "docx"),
                new ExtensionFilter("PDF Files", "pdf"),
                new ExtensionFilter("Text Files", "txt")
            };

            // Act
            var savePath = StandaloneFileBrowser.SaveFilePanel(title, directory, defaultName, extensions);

            // Assert
            Assert.IsNotNull(savePath, "Save path should not be null");
            
            // Note: Platform implementations may automatically add extensions
            // This test documents the expected behavior
            if (!string.IsNullOrEmpty(savePath))
            {
                var fileInfo = new FileInfo(savePath);
                // Some platforms may add the first extension automatically
                // This behavior is platform-specific and documented here
            }
        }

        #endregion

        #region Async Workflow Tests

        /// <summary>
        /// Tests complete async file selection workflow.
        /// </summary>
        [UnityTest]
        public IEnumerator CompleteAsyncFileSelectionWorkflow_WithCallback_CompletesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"C:\TestFiles";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = true;
            
            var (callback, result) = TestUtilities.CreateAsyncCallback<FileInfo[]>();

            // Act
            StandaloneFileBrowser.OpenFilePanelAsync(title, directory, extensions, multiselect, callback);

            // Wait for callback (in real implementation, this would be async)
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.IsTrue(result.WasCalled, "Async callback should be invoked");
            Assert.IsNotNull(result.Result, "Async result should not be null");
            Assert.IsInstanceOf<FileInfo[]>(result.Result, "Async result should be FileInfo array");
            
            // Verify callback timing
            Assert.Greater(result.CallTime, DateTime.MinValue, "Callback should have valid call time");
        }

        /// <summary>
        /// Tests complete async folder selection workflow.
        /// </summary>
        [UnityTest]
        public IEnumerator CompleteAsyncFolderSelectionWorkflow_WithCallback_CompletesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"C:\";
            const bool multiselect = false;
            
            var (callback, result) = TestUtilities.CreateAsyncCallback<DirectoryInfo[]>();

            // Act
            StandaloneFileBrowser.OpenFolderPanelAsync(title, directory, multiselect, callback);

            // Wait for callback
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.IsTrue(result.WasCalled, "Async callback should be invoked");
            Assert.IsNotNull(result.Result, "Async result should not be null");
            Assert.IsInstanceOf<DirectoryInfo[]>(result.Result, "Async result should be DirectoryInfo array");
        }

        /// <summary>
        /// Tests complete async save file workflow.
        /// </summary>
        [UnityTest]
        public IEnumerator CompleteAsyncSaveFileWorkflow_WithCallback_CompletesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"C:\Documents";
            const string defaultName = TestUtilities.TestFileNames.Document;
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            
            var (callback, result) = TestUtilities.CreateAsyncCallback<string>();

            // Act
            StandaloneFileBrowser.SaveFilePanelAsync(title, directory, defaultName, extensions, callback);

            // Wait for callback
            yield return new WaitForSeconds(0.1f);

            // Assert
            Assert.IsTrue(result.WasCalled, "Async callback should be invoked");
            Assert.IsNotNull(result.Result, "Async result should not be null");
            Assert.IsInstanceOf<string>(result.Result, "Async result should be string");
        }

        #endregion

        #region Error Recovery Workflow Tests

        /// <summary>
        /// Tests workflow recovery when user cancels dialog.
        /// </summary>
        [Test]
        public void WorkflowRecovery_WhenUserCancels_HandlesGracefully()
        {
            // Arrange - simulate user cancellation with empty results
            var mockBrowser = new MockStandaloneFileBrowser(shouldReturnEmpty: true);
            
            // Act & Assert - Test all dialog types handle cancellation
            Assert.DoesNotThrow(() =>
            {
                var fileResult = StandaloneFileBrowser.OpenFilePanel("Test", "", null, false);
                Assert.IsNotNull(fileResult, "File dialog cancellation should return empty array, not null");
                Assert.AreEqual(0, fileResult.Length, "Cancelled file dialog should return empty array");
                
                var folderResult = StandaloneFileBrowser.OpenFolderPanel("Test", "", false);
                Assert.IsNotNull(folderResult, "Folder dialog cancellation should return empty array, not null");
                Assert.AreEqual(0, folderResult.Length, "Cancelled folder dialog should return empty array");
                
                var saveResult = StandaloneFileBrowser.SaveFilePanel("Test", "", "", null);
                Assert.IsNotNull(saveResult, "Save dialog cancellation should return empty string, not null");
                Assert.AreEqual(string.Empty, saveResult, "Cancelled save dialog should return empty string");
            }, "All dialog types should handle user cancellation gracefully");
        }

        /// <summary>
        /// Tests workflow with invalid input parameters.
        /// </summary>
        [Test]
        public void WorkflowRecovery_WithInvalidInput_HandlesGracefully()
        {
            // Test various invalid input combinations
            var invalidInputs = new[]
            {
                new { title = (string)null, directory = (string)null, extensions = (ExtensionFilter[])null },
                new { title = "", directory = "", extensions = new ExtensionFilter[0] },
                new { title = "Valid", directory = @"C:\NonExistent\Path", extensions = TestUtilities.CreateStandardExtensionFilters() }
            };

            foreach (var input in invalidInputs)
            {
                Assert.DoesNotThrow(() =>
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel(input.title, input.directory, input.extensions, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel(input.title, input.directory, false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel(input.title, input.directory, "", input.extensions);
                    
                    Assert.IsNotNull(fileResult, "File dialog should handle invalid input gracefully");
                    Assert.IsNotNull(folderResult, "Folder dialog should handle invalid input gracefully");
                    Assert.IsNotNull(saveResult, "Save dialog should handle invalid input gracefully");
                }, $"Workflow should handle invalid input gracefully: {input}");
            }
        }

        #endregion

        #region Performance Integration Tests

        /// <summary>
        /// Tests workflow performance with large extension filter sets.
        /// </summary>
        [Test]
        public void WorkflowPerformance_WithLargeFilterSets_CompletesInReasonableTime()
        {
            // Arrange
            var largeFilterSet = new ExtensionFilter[100];
            for (int i = 0; i < largeFilterSet.Length; i++)
            {
                largeFilterSet[i] = new ExtensionFilter($"Filter {i}", $"ext{i}", $"alt{i}");
            }

            // Act & Assert
            var startTime = DateTime.UtcNow;
            
            Assert.DoesNotThrow(() =>
            {
                var result = StandaloneFileBrowser.OpenFilePanel("Performance Test", "", largeFilterSet, false);
                Assert.IsNotNull(result, "Large filter set should be processed successfully");
            }, "Workflow should handle large filter sets");

            var duration = DateTime.UtcNow - startTime;
            Assert.Less(duration.TotalSeconds, 5.0, "Workflow should complete within reasonable time");
        }

        /// <summary>
        /// Tests repeated workflow executions for memory leaks.
        /// </summary>
        [Test]
        public void WorkflowPerformance_RepeatedExecutions_DoesNotLeakMemory()
        {
            // Arrange
            const int iterations = 50;
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                for (int i = 0; i < iterations; i++)
                {
                    var fileResult = StandaloneFileBrowser.OpenFilePanel($"Test {i}", "", extensions, false);
                    var folderResult = StandaloneFileBrowser.OpenFolderPanel($"Test {i}", "", false);
                    var saveResult = StandaloneFileBrowser.SaveFilePanel($"Test {i}", "", $"file{i}.txt", extensions);
                    
                    Assert.IsNotNull(fileResult, $"File result should not be null on iteration {i}");
                    Assert.IsNotNull(folderResult, $"Folder result should not be null on iteration {i}");
                    Assert.IsNotNull(saveResult, $"Save result should not be null on iteration {i}");
                }
            }, "Repeated workflow executions should not cause memory issues");
        }

        #endregion
    }
}
