using System;
using NUnit.Framework;
using UnityEditor;
using USFB;
using USFB.Tests;

namespace USFB.Tests.Editor
{
    /// <summary>
    /// Tests for the StandaloneFileBrowserEditor platform implementation.
    /// These tests run in the Unity Editor and verify Editor-specific behavior.
    /// </summary>
    [TestFixture]
    public class EditorPlatformTests
    {
        private StandaloneFileBrowserEditor _editorBrowser;

        #region Setup and Teardown

        /// <summary>
        /// Sets up test environment before each test.
        /// </summary>
        [SetUp]
        public void SetUp()
        {
            _editorBrowser = new StandaloneFileBrowserEditor();
        }

        /// <summary>
        /// Cleans up test environment after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            _editorBrowser = null;
        }

        #endregion

        #region OpenFilePanel Tests

        /// <summary>
        /// Tests OpenFilePanel with null extensions uses EditorUtility.OpenFilePanel.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithNullExtensions_CallsCorrectEditorMethod()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"Assets";
            ExtensionFilter[] extensions = null;
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsInstanceOf<string[]>(result, "Result should be string array");
            }, "OpenFilePanel should handle null extensions correctly");
        }

        /// <summary>
        /// Tests OpenFilePanel with extensions uses EditorUtility.OpenFilePanelWithFilters.
        /// </summary>
        [Test]
        public void OpenFilePanel_WithExtensions_CallsCorrectEditorMethod()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"Assets";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsInstanceOf<string[]>(result, "Result should be string array");
            }, "OpenFilePanel should handle extensions correctly");
        }

        /// <summary>
        /// Tests OpenFilePanel returns empty array when no file is selected.
        /// </summary>
        [Test]
        public void OpenFilePanel_WhenCanceled_ReturnsEmptyArray()
        {
            // Note: This test documents expected behavior when user cancels the dialog
            // In actual Editor environment, this would require user interaction
            
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"Assets";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act
            var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);

            // Assert
            Assert.IsNotNull(result, "Result should not be null even when canceled");
            Assert.IsInstanceOf<string[]>(result, "Result should be string array");
            // Note: The actual length depends on user interaction in Editor
        }

        #endregion

        #region OpenFilePanelAsync Tests

        /// <summary>
        /// Tests OpenFilePanelAsync invokes callback immediately in Editor.
        /// </summary>
        [Test]
        public void OpenFilePanelAsync_InEditor_InvokesCallbackImmediately()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"Assets";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;
            
            bool callbackInvoked = false;
            string[] callbackResult = null;

            Action<string[]> callback = (result) =>
            {
                callbackInvoked = true;
                callbackResult = result;
            };

            // Act
            _editorBrowser.OpenFilePanelAsync(title, directory, extensions, multiselect, callback);

            // Assert
            Assert.IsTrue(callbackInvoked, "Callback should be invoked immediately in Editor");
            Assert.IsNotNull(callbackResult, "Callback result should not be null");
            Assert.IsInstanceOf<string[]>(callbackResult, "Callback result should be string array");
        }

        /// <summary>
        /// Tests OpenFilePanelAsync with null callback doesn't throw.
        /// </summary>
        [Test]
        public void OpenFilePanelAsync_WithNullCallback_DoesNotThrow()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.OpenFile;
            const string directory = @"Assets";
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;
            Action<string[]> callback = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                _editorBrowser.OpenFilePanelAsync(title, directory, extensions, multiselect, callback);
            }, "OpenFilePanelAsync should handle null callback gracefully");
        }

        #endregion

        #region OpenFolderPanel Tests

        /// <summary>
        /// Tests OpenFolderPanel uses EditorUtility.OpenFolderPanel.
        /// </summary>
        [Test]
        public void OpenFolderPanel_CallsCorrectEditorMethod()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"Assets";
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFolderPanel(title, directory, multiselect);
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsInstanceOf<string[]>(result, "Result should be string array");
            }, "OpenFolderPanel should work correctly");
        }

        /// <summary>
        /// Tests OpenFolderPanel with multiselect parameter.
        /// Note: Editor implementation may not support multiselect for folders.
        /// </summary>
        [Test]
        public void OpenFolderPanel_WithMultiselect_HandlesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"Assets";
            const bool multiselect = true;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFolderPanel(title, directory, multiselect);
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsInstanceOf<string[]>(result, "Result should be string array");
                // Note: Editor may return single folder even with multiselect=true
            }, "OpenFolderPanel should handle multiselect parameter");
        }

        #endregion

        #region OpenFolderPanelAsync Tests

        /// <summary>
        /// Tests OpenFolderPanelAsync invokes callback immediately in Editor.
        /// </summary>
        [Test]
        public void OpenFolderPanelAsync_InEditor_InvokesCallbackImmediately()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SelectFolder;
            const string directory = @"Assets";
            const bool multiselect = false;
            
            bool callbackInvoked = false;
            string[] callbackResult = null;

            Action<string[]> callback = (result) =>
            {
                callbackInvoked = true;
                callbackResult = result;
            };

            // Act
            _editorBrowser.OpenFolderPanelAsync(title, directory, multiselect, callback);

            // Assert
            Assert.IsTrue(callbackInvoked, "Callback should be invoked immediately in Editor");
            Assert.IsNotNull(callbackResult, "Callback result should not be null");
            Assert.IsInstanceOf<string[]>(callbackResult, "Callback result should be string array");
        }

        #endregion

        #region SaveFilePanel Tests

        /// <summary>
        /// Tests SaveFilePanel uses EditorUtility.SaveFilePanel.
        /// </summary>
        [Test]
        public void SaveFilePanel_CallsCorrectEditorMethod()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"Assets";
            const string defaultName = TestUtilities.TestFileNames.Document;
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.SaveFilePanel(title, directory, defaultName, extensions);
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsInstanceOf<string>(result, "Result should be string");
            }, "SaveFilePanel should work correctly");
        }

        /// <summary>
        /// Tests SaveFilePanel with null extensions.
        /// </summary>
        [Test]
        public void SaveFilePanel_WithNullExtensions_HandlesCorrectly()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"Assets";
            const string defaultName = TestUtilities.TestFileNames.Document;
            ExtensionFilter[] extensions = null;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.SaveFilePanel(title, directory, defaultName, extensions);
                Assert.IsNotNull(result, "Result should not be null");
                Assert.IsInstanceOf<string>(result, "Result should be string");
            }, "SaveFilePanel should handle null extensions");
        }

        #endregion

        #region SaveFilePanelAsync Tests

        /// <summary>
        /// Tests SaveFilePanelAsync invokes callback immediately in Editor.
        /// </summary>
        [Test]
        public void SaveFilePanelAsync_InEditor_InvokesCallbackImmediately()
        {
            // Arrange
            const string title = TestUtilities.TestTitles.SaveFile;
            const string directory = @"Assets";
            const string defaultName = TestUtilities.TestFileNames.Document;
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            
            bool callbackInvoked = false;
            string callbackResult = null;

            Action<string> callback = (result) =>
            {
                callbackInvoked = true;
                callbackResult = result;
            };

            // Act
            _editorBrowser.SaveFilePanelAsync(title, directory, defaultName, extensions, callback);

            // Assert
            Assert.IsTrue(callbackInvoked, "Callback should be invoked immediately in Editor");
            Assert.IsNotNull(callbackResult, "Callback result should not be null");
            Assert.IsInstanceOf<string>(callbackResult, "Callback result should be string");
        }

        #endregion

        #region Filter Utility Tests

        /// <summary>
        /// Tests GetFilterFromFileExtensionList utility method with standard filters.
        /// </summary>
        [Test]
        public void GetFilterFromFileExtensionList_WithStandardFilters_ReturnsCorrectFormat()
        {
            // Note: This tests the private method indirectly through public API
            // The actual filter format is specific to EditorUtility.OpenFilePanelWithFilters
            
            // Arrange
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const string title = "Test";
            const string directory = @"Assets";
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                // The method should process the extensions without throwing
                Assert.IsNotNull(result, "Result should not be null");
            }, "Filter processing should work correctly");
        }

        /// <summary>
        /// Tests GetFilterFromFileExtensionList with edge case filters.
        /// </summary>
        [Test]
        public void GetFilterFromFileExtensionList_WithEdgeCaseFilters_HandlesCorrectly()
        {
            // Arrange
            var extensions = TestUtilities.CreateEdgeCaseExtensionFilters();
            const string title = "Test";
            const string directory = @"Assets";
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                // The method should handle edge cases without throwing
                Assert.IsNotNull(result, "Result should not be null with edge case filters");
            }, "Filter processing should handle edge cases");
        }

        #endregion

        #region Editor-Specific Behavior Tests

        /// <summary>
        /// Tests that Editor implementation works with Unity project paths.
        /// </summary>
        [Test]
        public void EditorImplementation_WithUnityProjectPaths_WorksCorrectly()
        {
            // Arrange
            const string title = "Select Unity Asset";
            const string directory = "Assets"; // Unity project relative path
            var extensions = new[] { new ExtensionFilter("Unity Assets", "prefab", "asset", "cs") };
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with Unity paths");
            }, "Editor implementation should work with Unity project paths");
        }

        /// <summary>
        /// Tests that Editor implementation handles empty directory correctly.
        /// </summary>
        [Test]
        public void EditorImplementation_WithEmptyDirectory_UsesProjectRoot()
        {
            // Arrange
            const string title = "Test";
            const string directory = ""; // Empty directory should default to project root
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            const bool multiselect = false;

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = _editorBrowser.OpenFilePanel(title, directory, extensions, multiselect);
                Assert.IsNotNull(result, "Result should not be null with empty directory");
            }, "Editor implementation should handle empty directory");
        }

        #endregion
    }
}
