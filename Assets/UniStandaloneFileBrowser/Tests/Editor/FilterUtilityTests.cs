using System;
using System.Reflection;
using NUnit.Framework;
using USFB;
using USFB.Tests;

namespace USFB.Tests.Editor
{
    /// <summary>
    /// Tests for filter utility methods used across different platform implementations.
    /// These tests verify the correct conversion of ExtensionFilter arrays to platform-specific formats.
    /// </summary>
    [TestFixture]
    public class FilterUtilityTests
    {
        #region Editor Filter Tests

        /// <summary>
        /// Tests the Editor's GetFilterFromFileExtensionList method with standard filters.
        /// </summary>
        [Test]
        public void EditorGetFilterFromFileExtensionList_WithStandardFilters_ReturnsCorrectFormat()
        {
            // Arrange
            var editorBrowser = new StandaloneFileBrowserEditor();
            var extensions = TestUtilities.CreateStandardExtensionFilters();

            // Use reflection to access the private method
            var method = typeof(StandaloneFileBrowserEditor).GetMethod("GetFilterFromFileExtensionList", 
                BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var result = method?.Invoke(null, new object[] { extensions }) as string[];

            // Assert
            Assert.IsNotNull(result, "Filter result should not be null");
            Assert.Greater(result.Length, 0, "Filter result should contain elements");
            Assert.AreEqual(extensions.Length * 2, result.Length, 
                "Editor filter format should have 2 elements per extension filter (name and extensions)");

            // Verify the format: [name1, extensions1, name2, extensions2, ...]
            for (int i = 0; i < extensions.Length; i++)
            {
                int nameIndex = i * 2;
                int extensionsIndex = i * 2 + 1;
                
                Assert.AreEqual(extensions[i].Name, result[nameIndex], 
                    $"Filter name at index {nameIndex} should match");
                
                string expectedExtensions = string.Join(",", extensions[i].Extensions);
                Assert.AreEqual(expectedExtensions, result[extensionsIndex], 
                    $"Filter extensions at index {extensionsIndex} should match");
            }
        }

        /// <summary>
        /// Tests the Editor's GetFilterFromFileExtensionList method with empty filters.
        /// </summary>
        [Test]
        public void EditorGetFilterFromFileExtensionList_WithEmptyFilters_ReturnsEmptyArray()
        {
            // Arrange
            var extensions = TestUtilities.CreateEmptyExtensionFilters();

            // Use reflection to access the private method
            var method = typeof(StandaloneFileBrowserEditor).GetMethod("GetFilterFromFileExtensionList", 
                BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var result = method?.Invoke(null, new object[] { extensions }) as string[];

            // Assert
            Assert.IsNotNull(result, "Filter result should not be null");
            Assert.AreEqual(0, result.Length, "Empty filters should return empty array");
        }

        /// <summary>
        /// Tests the Editor's GetFilterFromFileExtensionList method with edge case filters.
        /// </summary>
        [Test]
        public void EditorGetFilterFromFileExtensionList_WithEdgeCaseFilters_HandlesCorrectly()
        {
            // Arrange
            var extensions = TestUtilities.CreateEdgeCaseExtensionFilters();

            // Use reflection to access the private method
            var method = typeof(StandaloneFileBrowserEditor).GetMethod("GetFilterFromFileExtensionList", 
                BindingFlags.NonPublic | BindingFlags.Static);

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = method?.Invoke(null, new object[] { extensions }) as string[];
                Assert.IsNotNull(result, "Filter result should not be null with edge case filters");
            }, "Editor filter method should handle edge cases gracefully");
        }

        #endregion

        #region Cross-Platform Filter Format Tests

        /// <summary>
        /// Tests that different platform implementations handle the same filters consistently.
        /// </summary>
        [Test]
        public void CrossPlatformFilters_WithSameInput_ProduceConsistentBehavior()
        {
            // Arrange
            var extensions = TestUtilities.CreateStandardExtensionFilters();
            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert - Test that all platforms can process the same filter set
            Assert.DoesNotThrow(() =>
            {
                // Editor implementation
                var editorResult = editorBrowser.OpenFilePanel("Test", "", extensions, false);
                Assert.IsNotNull(editorResult, "Editor should handle standard filters");

                // Note: Other platform implementations would be tested here if we had access to them
                // or if we could mock their dependencies
            }, "All platforms should handle standard filters consistently");
        }

        /// <summary>
        /// Tests filter handling with special characters and Unicode.
        /// </summary>
        [Test]
        public void FilterUtilities_WithSpecialCharacters_HandleCorrectly()
        {
            // Arrange
            var specialFilters = new[]
            {
                new ExtensionFilter("Files with spaces", "file ext", "another ext"),
                new ExtensionFilter("Unicode 文件", "测试", "файл"),
                new ExtensionFilter("Special@#$%", "ext@", "ext#"),
                new ExtensionFilter("", "empty-name-filter")
            };

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", specialFilters, false);
                Assert.IsNotNull(result, "Should handle special character filters");
            }, "Filter utilities should handle special characters and Unicode");
        }

        /// <summary>
        /// Tests filter handling with very long names and extensions.
        /// </summary>
        [Test]
        public void FilterUtilities_WithLongNamesAndExtensions_HandleCorrectly()
        {
            // Arrange
            string longName = new string('A', 500);
            string longExtension = new string('x', 100);
            
            var longFilters = new[]
            {
                new ExtensionFilter(longName, longExtension),
                new ExtensionFilter("Normal", "txt"),
                new ExtensionFilter("Multiple Long", longExtension, "another" + longExtension)
            };

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", longFilters, false);
                Assert.IsNotNull(result, "Should handle long filter names and extensions");
            }, "Filter utilities should handle very long names and extensions");
        }

        #endregion

        #region Filter Validation Tests

        /// <summary>
        /// Tests that filters with null extensions are handled correctly.
        /// </summary>
        [Test]
        public void FilterUtilities_WithNullExtensions_HandleGracefully()
        {
            // Arrange
            var filtersWithNulls = new[]
            {
                new ExtensionFilter("Valid Filter", "txt", "doc"),
                new ExtensionFilter("Null Extensions Filter", (string[])null),
                new ExtensionFilter("Another Valid", "pdf")
            };

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", filtersWithNulls, false);
                Assert.IsNotNull(result, "Should handle filters with null extensions");
            }, "Filter utilities should handle null extensions gracefully");
        }

        /// <summary>
        /// Tests that filters with empty extension arrays are handled correctly.
        /// </summary>
        [Test]
        public void FilterUtilities_WithEmptyExtensionArrays_HandleGracefully()
        {
            // Arrange
            var filtersWithEmpty = new[]
            {
                new ExtensionFilter("Valid Filter", "txt", "doc"),
                new ExtensionFilter("Empty Extensions", new string[0]),
                new ExtensionFilter("Another Valid", "pdf")
            };

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", filtersWithEmpty, false);
                Assert.IsNotNull(result, "Should handle filters with empty extension arrays");
            }, "Filter utilities should handle empty extension arrays gracefully");
        }

        /// <summary>
        /// Tests that duplicate extensions within a filter are preserved.
        /// </summary>
        [Test]
        public void FilterUtilities_WithDuplicateExtensions_PreserveDuplicates()
        {
            // Arrange
            var filtersWithDuplicates = new[]
            {
                new ExtensionFilter("Duplicate Extensions", "txt", "doc", "txt", "pdf", "doc"),
                new ExtensionFilter("Normal Filter", "png", "jpg")
            };

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", filtersWithDuplicates, false);
                Assert.IsNotNull(result, "Should handle filters with duplicate extensions");
            }, "Filter utilities should preserve duplicate extensions");
        }

        #endregion

        #region Performance Tests

        /// <summary>
        /// Tests filter processing performance with large number of filters.
        /// </summary>
        [Test]
        public void FilterUtilities_WithManyFilters_PerformsWell()
        {
            // Arrange
            var manyFilters = new ExtensionFilter[1000];
            for (int i = 0; i < manyFilters.Length; i++)
            {
                manyFilters[i] = new ExtensionFilter($"Filter {i}", $"ext{i}", $"alt{i}");
            }

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            var startTime = DateTime.UtcNow;
            
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", manyFilters, false);
                Assert.IsNotNull(result, "Should handle many filters");
            }, "Filter utilities should handle large number of filters");

            var duration = DateTime.UtcNow - startTime;
            Assert.Less(duration.TotalSeconds, 5.0, "Filter processing should complete within reasonable time");
        }

        /// <summary>
        /// Tests filter processing performance with filters containing many extensions.
        /// </summary>
        [Test]
        public void FilterUtilities_WithManyExtensionsPerFilter_PerformsWell()
        {
            // Arrange
            var extensionsArray = new string[500];
            for (int i = 0; i < extensionsArray.Length; i++)
            {
                extensionsArray[i] = $"ext{i}";
            }

            var filterWithManyExtensions = new[]
            {
                new ExtensionFilter("Many Extensions Filter", extensionsArray),
                new ExtensionFilter("Normal Filter", "txt", "doc")
            };

            var editorBrowser = new StandaloneFileBrowserEditor();

            // Act & Assert
            var startTime = DateTime.UtcNow;
            
            Assert.DoesNotThrow(() =>
            {
                var result = editorBrowser.OpenFilePanel("Test", "", filterWithManyExtensions, false);
                Assert.IsNotNull(result, "Should handle filters with many extensions");
            }, "Filter utilities should handle filters with many extensions");

            var duration = DateTime.UtcNow - startTime;
            Assert.Less(duration.TotalSeconds, 5.0, "Filter processing should complete within reasonable time");
        }

        #endregion

        #region Integration with TestUtilities

        /// <summary>
        /// Tests that all TestUtilities filter sets work with filter processing.
        /// </summary>
        [Test]
        public void FilterUtilities_WithAllTestUtilityFilters_WorkCorrectly()
        {
            // Arrange
            var editorBrowser = new StandaloneFileBrowserEditor();
            var filterSets = new[]
            {
                TestUtilities.CreateStandardExtensionFilters(),
                TestUtilities.CreateEdgeCaseExtensionFilters(),
                TestUtilities.CreateEmptyExtensionFilters()
            };

            // Act & Assert
            foreach (var filterSet in filterSets)
            {
                Assert.DoesNotThrow(() =>
                {
                    var result = editorBrowser.OpenFilePanel("Test", "", filterSet, false);
                    Assert.IsNotNull(result, $"Should handle filter set: {filterSet?.GetType().Name}");
                }, $"Filter utilities should work with all TestUtilities filter sets");
            }
        }

        #endregion
    }
}
