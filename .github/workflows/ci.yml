name: CI
on:
  push:
    branches:
    - master
jobs:
  release:
    if: "!contains(github.event.head_commit.message, 'skip ci')"
    name: Release
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0

    - name: Semantic Release
      id: semantic
      uses: cycjimmy/semantic-release-action@v2.1.3
      with:
        extra_plugins: |
          @semantic-release/changelog
          @semantic-release/git
        branch: master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Create UPM branch
      run: |
        git branch -d upm &> /dev/null || echo upm branch not found
        git subtree split -P "$PKG_ROOT" -b upm
        git checkout upm
        if [[ -d "Samples" ]]; then
          git mv Samples Samples~
          rm -f Samples.meta
          git config --global user.name 'github-bot'
          git config --global user.email '<EMAIL>'
          git commit -am "fix: Samples => Samples~"
        fi
        git push -f -u origin upm
      env:
        PKG_ROOT: "Assets/UniStandaloneFileBrowser"

    - name: Create UPM git tag
      if: steps.semantic.outputs.new_release_published == 'true'
      run: |
        git tag $TAG upm
        git push origin --tags
      env:
        TAG: upm/v${{ steps.semantic.outputs.new_release_version }}