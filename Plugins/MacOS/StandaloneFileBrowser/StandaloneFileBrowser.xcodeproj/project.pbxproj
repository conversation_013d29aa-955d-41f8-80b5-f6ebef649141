// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 45;
	objects = {

/* Begin PBXBuildFile section */
		6A3F7F8310AC147200948A73 /* Plugin.mm in Sources */ = {isa = PBXBuildFile; fileRef = 6A3F7D2310AAFB3D00948A73 /* Plugin.mm */; };
		DD2760011CA14C26008BCDB8 /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DD2760001CA14C26008BCDB8 /* AppKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		089C167EFE841241C02AAC07 /* English */ = {isa = PBXFileReference; fileEncoding = 10; lastKnownFileType = text.plist.strings; name = English; path = English.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		08EA7FFBFE8413EDC02AAC07 /* Carbon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Carbon.framework; path = /System/Library/Frameworks/Carbon.framework; sourceTree = "<absolute>"; };
		32BAE0B30371A71500C91783 /* Plugin.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Plugin.pch; sourceTree = "<group>"; };
		6A3F7D2310AAFB3D00948A73 /* Plugin.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = Plugin.mm; sourceTree = "<group>"; };
		6A3F7F7E10AC146D00948A73 /* StandaloneFileBrowser.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = StandaloneFileBrowser.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		8D01CCD10486CAD60068D4B7 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		DD2760001CA14C26008BCDB8 /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = ../../../../../../../../../System/Library/Frameworks/AppKit.framework; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6A3F7F7C10AC146D00948A73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DD2760011CA14C26008BCDB8 /* AppKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		089C166AFE841209C02AAC07 /* XCodePlugin */ = {
			isa = PBXGroup;
			children = (
				08FB77ADFE841716C02AAC07 /* Source */,
				089C167CFE841241C02AAC07 /* Resources */,
				089C1671FE841209C02AAC07 /* External Frameworks and Libraries */,
				19C28FB4FE9D528D11CA2CBB /* Products */,
			);
			name = XCodePlugin;
			sourceTree = "<group>";
		};
		089C1671FE841209C02AAC07 /* External Frameworks and Libraries */ = {
			isa = PBXGroup;
			children = (
				DD2760001CA14C26008BCDB8 /* AppKit.framework */,
				08EA7FFBFE8413EDC02AAC07 /* Carbon.framework */,
			);
			name = "External Frameworks and Libraries";
			sourceTree = "<group>";
		};
		089C167CFE841241C02AAC07 /* Resources */ = {
			isa = PBXGroup;
			children = (
				8D01CCD10486CAD60068D4B7 /* Info.plist */,
				089C167DFE841241C02AAC07 /* InfoPlist.strings */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		08FB77ADFE841716C02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				6A3F7D2310AAFB3D00948A73 /* Plugin.mm */,
				32BAE0B30371A71500C91783 /* Plugin.pch */,
			);
			name = Source;
			sourceTree = "<group>";
		};
		19C28FB4FE9D528D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				6A3F7F7E10AC146D00948A73 /* StandaloneFileBrowser.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6A3F7F7D10AC146D00948A73 /* StandaloneFileBrowser */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6A3F7F8210AC146D00948A73 /* Build configuration list for PBXNativeTarget "StandaloneFileBrowser" */;
			buildPhases = (
				6A3F7F7A10AC146D00948A73 /* Resources */,
				6A3F7F7B10AC146D00948A73 /* Sources */,
				6A3F7F7C10AC146D00948A73 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = StandaloneFileBrowser;
			productName = ASimplePlugin;
			productReference = 6A3F7F7E10AC146D00948A73 /* StandaloneFileBrowser.bundle */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		089C1669FE841209C02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
			};
			buildConfigurationList = 4FADC23708B4156C00ABE55E /* Build configuration list for PBXProject "StandaloneFileBrowser" */;
			compatibilityVersion = "Xcode 3.1";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 089C166AFE841209C02AAC07 /* XCodePlugin */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6A3F7F7D10AC146D00948A73 /* StandaloneFileBrowser */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6A3F7F7A10AC146D00948A73 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6A3F7F7B10AC146D00948A73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A3F7F8310AC147200948A73 /* Plugin.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		089C167DFE841241C02AAC07 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				089C167EFE841241C02AAC07 /* English */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		4FADC23808B4156C00ABE55E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_64_BIT)";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = NO;
				PREBINDING = NO;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		4FADC23908B4156C00ABE55E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_64_BIT)";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = NO;
				PREBINDING = NO;
				SDKROOT = macosx;
			};
			name = Release;
		};
		6A3F7F8010AC146D00948A73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_FIX_AND_CONTINUE = YES;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/Carbon.framework/Headers/Carbon.h";
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				INSTALL_PATH = "$(HOME)/Library/Bundles";
				OTHER_LDFLAGS = (
					"-framework",
					Carbon,
				);
				PREBINDING = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.gkngkc.sfb;
				PRODUCT_NAME = StandaloneFileBrowser;
				VALID_ARCHS = x86_64;
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		6A3F7F8110AC146D00948A73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_ENABLE_FIX_AND_CONTINUE = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/Carbon.framework/Headers/Carbon.h";
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				INSTALL_PATH = "$(HOME)/Library/Bundles";
				OTHER_LDFLAGS = (
					"-framework",
					Carbon,
				);
				PREBINDING = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.gkngkc.sfb;
				PRODUCT_NAME = StandaloneFileBrowser;
				VALID_ARCHS = x86_64;
				WRAPPER_EXTENSION = bundle;
				ZERO_LINK = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4FADC23708B4156C00ABE55E /* Build configuration list for PBXProject "StandaloneFileBrowser" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4FADC23808B4156C00ABE55E /* Debug */,
				4FADC23908B4156C00ABE55E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6A3F7F8210AC146D00948A73 /* Build configuration list for PBXNativeTarget "StandaloneFileBrowser" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6A3F7F8010AC146D00948A73 /* Debug */,
				6A3F7F8110AC146D00948A73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 089C1669FE841209C02AAC07 /* Project object */;
}
